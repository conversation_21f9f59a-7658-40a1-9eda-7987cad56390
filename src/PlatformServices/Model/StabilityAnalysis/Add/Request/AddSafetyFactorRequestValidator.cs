using FluentValidation;
using Model._Shared.File;

namespace Model.StabilityAnalysis.Add.Request
{
    public sealed class AddSafetyFactorRequestValidator : AbstractValidator<AddSafetyFactorRequest>
    {
        public AddSafetyFactorRequestValidator()
        {
            RuleFor(request => request.Values)
                .NotEmpty();

            RuleForEach(request => request.Values)
                .SetValidator(new AddSafetyFactorValueValidator());

            RuleFor(request => request.SliFile)
                .NotEmpty()
                .SetValidator(new FileValidator(10, new[] { "sli" }));

            RuleFor(request => request.SltmFile)
                .SetValidator(new FileValidator(10, new[] { "sltm" }));
        }
    }
}

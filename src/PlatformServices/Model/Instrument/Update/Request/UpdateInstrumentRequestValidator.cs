using FluentValidation;
using System;
using System.Linq;

namespace Model.Instrument.Update.Request
{
    public class UpdateInstrumentRequestValidator : AbstractValidator<UpdateInstrumentRequest>
    {
        private readonly _Shared.InstrumentRequestValidator _instrumentValidator = new();

        public UpdateInstrumentRequestValidator()
        {
            CascadeMode = CascadeMode.Stop;

            RuleFor(x => x.Instruments)
                .NotEmpty();

            RuleForEach(x => x.Instruments)
                .Must(x => x.Id != Guid.Empty)
                .WithMessage("All instruments must have an ID to be able to edit.");

            RuleForEach(x => x.Instruments)
                .SetValidator(_instrumentValidator);

            RuleFor(x => x.RequestedBy)
                .NotEmpty();

            RuleFor(x => x.RequestedUserStructures)
                .NotEmpty()
                .When(x => !x.RequestedBySuperSupport);

            RuleFor(x => x)
               .Must(x => !x.Instruments.Select(y => y.Structure.Id).Except(x.RequestedUserStructures).Any())
               .When(x => !x.RequestedBySuperSupport)
               .WithMessage("There are instruments tied to structures that you cannot access.");

            RuleFor(x => x)
               .Must(x => x.Instruments.Where(y => !string.IsNullOrEmpty(y.AlternativeName)).GroupBy(y => y.AlternativeName?.ToLower()).All(y => y.Count() == 1))
               .WithMessage("Instrument alternative name must be unique.");

            RuleFor(x => x)
               .Must(x => x.Instruments.GroupBy(y => y.Identifier.ToLower()).All(y => y.Count() == 1))
               .WithMessage("Instrument identifier must be unique.");

            RuleFor(x => x)
               .Must(x => x.Instruments.Select(y => y.Measurements).Where(y => y != null).All(y => y.GroupBy(z => z.Identifier?.ToLower()).All(z => z.Count() == 1)))
               .WithMessage("Pressure cell, measurement points and magnetic rings identifier must be unique.");

            RuleFor(x => x)
               .Must(x => x.Instruments.Select(y => y.Measurements).Where(y => y != null).All(y => y.Select(z => z.AlternativeName?.ToLower()).Where(z => !string.IsNullOrEmpty(z)).GroupBy(z => z).All(z => z.Count() == 1)))
               .WithMessage("Pressure cell, measurement points and magnetic rings alternative name must be unique.");
        }
    }
}

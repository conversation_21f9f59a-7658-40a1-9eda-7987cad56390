using FluentValidation;

namespace Model.Instrument.AddSecurityLevelAlert.Request
{
    public sealed class SecurityLevelAlertRequestValidator : AbstractValidator<SecurityLevelAlertRequest>
    {
        public SecurityLevelAlertRequestValidator()
        {
            CascadeMode = CascadeMode.Stop;

            RuleFor(x => x.InstrumentId)
                .NotEmpty();

            RuleFor(x => x.ReadingValueDate)
                .NotEmpty();

            RuleFor(x => x.SecurityLevel)
                .NotEmpty();
        }
    }
}

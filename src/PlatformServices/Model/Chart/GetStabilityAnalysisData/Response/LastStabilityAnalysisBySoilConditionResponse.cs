using Domain.Enums;
using System;
using System.Collections.Generic;

namespace Model.Chart.GetStabilityAnalysisData.Response
{
    public sealed record LastStabilityAnalysisBySoilConditionResponse
    {
        public DateTime ReadingCreatedDate { get; set; }
        public bool IsLowestSafetyFactor { get; set; }
        public IEnumerable<LastSafetyFactorValue> Values { get; set; }
    }

    public sealed record LastSafetyFactorValue
    {
        public SliFileType SliFileType { get; set; }
        public double Value { get; set; }
        public CalculationMethod CalculationMethod { get; set; }
    }
}

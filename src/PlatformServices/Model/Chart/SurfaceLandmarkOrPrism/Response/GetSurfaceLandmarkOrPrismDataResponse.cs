using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Model.Chart.SurfaceLandmarkOrPrism.Response
{
    public sealed record GetSurfaceLandmarkOrPrismDataResponse
    {
        [JsonPropertyName("instrument_identifier")]
        public string InstrumentIdentifier { get; set; }

        [JsonPropertyName("readings")]
        public List<SurfaceLandmarkOrPrismReading> Readings { get; set; }
    }
}

using FluentValidation;
using Model._Shared.File;
using Model.InspectionSheet._Shared;

namespace Model.InspectionSheet.Update.Validators
{
    public sealed class OccurrenceAttachmentRequestValidator : AbstractValidator<OccurrenceAttachment>
    {
        private readonly FileValidator _fileValidator = new(10, new[] { "jpg", "png", "jpeg", "mp4", "pdf", "docx", "dxf", "txt" });

        public OccurrenceAttachmentRequestValidator()
        {
            CascadeMode = CascadeMode.Stop;

            RuleFor(request => request.Easting)
                .InclusiveBetween(100000, 900000)
                .WithMessage("Easting must be between 100.000 and 900.000 meters.");

            RuleFor(request => request.Northing)
                .InclusiveBetween(0, 10000000)
                .WithMessage("Northing must be between 0 and 10.000.000 meters.");

            RuleFor(x => x.File)
                .SetValidator(_fileValidator)
                .When(x => x.File != null);
        }
    }
}

using Domain.Enums;
using System;
using System.Text.Json.Serialization;

namespace Model.Structure.GetStabilityAnalysisDate.Request
{
    public sealed record GetStabilityAnalysisDateRequest : RequestWithMetadata
    {
        [JsonIgnore]
        public Guid StructureId { get; set; }
        public SurfaceType? SurfaceType { get; set; }
        public CalculationMethod? CalculationMethod { get; set; }
    }
}

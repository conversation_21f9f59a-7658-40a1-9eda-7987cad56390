using System;
using Model.Core.Search.DateFilter;
using Model.Maps._Shared;

namespace Model.Maps.GetDisplacementMap.Request
{
    public sealed record GetDisplacementMapRequest : GetInstrumentMapRequest
    {
        public DatePeriod? Period { get; set; } = DatePeriod.Custom;
        public DateTime StartDate { get; init; } = DateTime.Now.AddDays(-1);
        public DateTime EndDate { get; init; } = DateTime.Now;
        public double Overkill { get; init; }
    }
}
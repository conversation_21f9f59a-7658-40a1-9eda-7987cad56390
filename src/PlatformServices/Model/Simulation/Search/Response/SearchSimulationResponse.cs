using Domain.Enums;
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Model.Simulation.Search.Response
{
    public sealed record SearchSimulationResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; init; }

        [JsonPropertyName("name")]
        public string Name { get; init; }

        [JsonPropertyName("should_keep")]
        public bool ShouldKeep { get; init; }

        [JsonPropertyName("status")]
        public SimulationStatus Status { get; init; }

        [JsonPropertyName("search_identifier")]
        public int SearchIdentifier { get; init; }

        [JsonPropertyName("created_by")]
        public _Shared.User.User CreatedBy { get; init; }

        [JsonPropertyName("created_date")]
        public DateTime CreatedDate { get; init; }

        [JsonPropertyName("need_to_do_statistical_calculations")]
        public bool NeedToDoStatisticalCalculations { get; init; }

        [JsonPropertyName("sections")]
        public List<SearchSimulationSection> Sections { get; init; }
    }
}

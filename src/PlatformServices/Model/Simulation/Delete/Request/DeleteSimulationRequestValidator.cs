using FluentValidation;

namespace Model.Simulation.Delete.Request
{
    public class DeleteSimulationRequestValidator : AbstractValidator<DeleteSimulationRequest>
    {
        public DeleteSimulationRequestValidator()
        {
            CascadeMode = CascadeMode.Stop;

            RuleFor(x => x.SimulationId)
                .NotEmpty();

            RuleFor(x => x.RequestedBy)
                .NotEmpty();
        }
    }
}

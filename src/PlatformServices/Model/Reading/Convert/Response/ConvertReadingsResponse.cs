using Model.Reading._Shared.ConvertReadings;
using Model.Reading.GetTemplateFile.Response;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Model.Reading.Convert.Response
{
    public sealed record ConvertReadingsResponse
    {
        [JsonPropertyName("readings")]
        public List<ConvertedReading> Readings { get; set; }

        [JsonPropertyName("errors_file")]
        public GetReadingTemplateFileResponse ErrorsFile { get; set; }
    }
}

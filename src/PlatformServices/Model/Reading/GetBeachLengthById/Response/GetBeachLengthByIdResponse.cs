using System;
using System.Text.Json.Serialization;

namespace Model.Reading.GetBeachLengthById.Response
{
    public sealed record GetBeachLengthByIdResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("section")]
        public Model._Shared.Section.Section Section { get; set; }

        [JsonPropertyName("date")]
        public DateTime Date { get; set; }

        [JsonPropertyName("length")]
        public decimal Length { get; set; }
    }
}

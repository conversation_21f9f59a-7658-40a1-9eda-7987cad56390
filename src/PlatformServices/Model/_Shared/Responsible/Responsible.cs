using System;
using System.Text.Json.Serialization;

namespace Model._Shared.Responsible
{
    public sealed record Responsible
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("email_address")]
        public string EmailAddress { get; set; }

        [Json<PERSON>ropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("position")]
        public Position.Position Position { get; set; }

        [Json<PERSON>ropertyName("role")]
        public Role.ResponsibleRole Role { get; set; }

        [JsonPropertyName("professional_record")]
        public string ProfessionalRecord { get; set; }

        [JsonPropertyName("cpf")]
        public string Cpf { get; set; }
    }
}

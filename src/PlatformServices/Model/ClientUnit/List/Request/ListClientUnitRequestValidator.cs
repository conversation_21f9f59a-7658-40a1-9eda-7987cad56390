using FluentValidation;

namespace Model.ClientUnit.List.Request
{
    public class ListClientUnitRequestValidator : AbstractValidator<ListClientUnitRequest>
    {
        public ListClientUnitRequestValidator()
        {
            CascadeMode = CascadeMode.Stop;

            RuleFor(x => x.RequestedBy)
                .NotEmpty();

            RuleFor(x => x.RequestedUserClientUnits)
                .NotEmpty()
                .When(x => !x.RequestedBySuperSupport);
        }
    }
}

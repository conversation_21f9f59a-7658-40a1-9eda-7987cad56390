using Domain.Entities;
using Domain.Enums;
using Model.StaticMaterial._Shared;
using Model.StaticMaterial._Shared.PointValue;
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Model.StaticMaterial.GetById.Response
{
    public sealed record GetStaticMaterialByIdResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("search_identifier")]
        public int SearchIdentifier { get; set; }

        [JsonPropertyName("start_date")]
        public DateTime StartDate { get; set; }

        [JsonPropertyName("client")]
        public Model._Shared.Client.Client Client { get; set; }

        [JsonPropertyName("client_unit")]
        public Model._Shared.ClientUnit.ClientUnit ClientUnit { get; set; }

        [JsonPropertyName("structure")]
        public Model._Shared.Structure.Structure Structure { get; set; }

        [JsonPropertyName("created_by")]
        public Model._Shared.User.User CreatedBy { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("active")]
        public bool Active { get; set; }

        [JsonPropertyName("drained_static_material_value")]
        public StaticMaterialValueModel DrainedStaticMaterialValue { get; set; }

        [JsonPropertyName("undrained_static_material_value")]
        public StaticMaterialValueModel UndrainedStaticMaterialValue { get; set; }

        [JsonPropertyName("pseudo_static_static_material_value")]
        public StaticMaterialValueModel PseudoStaticStaticMaterialValue { get; set; }

        [JsonPropertyName("static_material_reviews")]
        public List<StaticMaterialReviewModel> Reviews { get; set; }
    }
}

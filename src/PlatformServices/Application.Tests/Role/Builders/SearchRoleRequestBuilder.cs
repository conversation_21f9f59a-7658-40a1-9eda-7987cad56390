using Model.Role.Search.Request;

namespace Application.Tests.Role.Builders
{
    public static class SearchRoleRequestBuilder
    {
        public static SearchRoleRequest CreateDefault()
        {
            return new()
            {
                Name = "Lorem",
                Page = 1,
                PageSize = 15,
                SearchIdentifier = 1
            };
        }

        public static SearchRoleRequest WithRequest(
            this SearchRoleRequest request,
            string name,
            int page,
            int pageSize,
            int searchIdentifier)
        {
            request.Name = name;
            request.Page = page;
            request.PageSize = pageSize;
            request.SearchIdentifier = searchIdentifier;

            return request;
        }
    }
}

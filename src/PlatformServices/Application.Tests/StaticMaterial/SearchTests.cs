using Application.Core;
using Application.StaticMaterial.Search;
using Application.Tests.Instrument.Builders;
using Application.Tests.StaticMaterial.Builders;
using Database.Repositories.StaticMaterial;
using Database.Repositories.User;
using FluentAssertions;
using Model.StaticMaterial.Search.Request;
using Moq;
using System.Threading.Tasks;
using Xunit;

namespace Application.Tests.StaticMaterial
{
    [Trait("SearchStaticMaterialUseCase", "Execute")]
    public class SearchTests
    {
        private readonly Mock<IStaticMaterialRepository> _staticMaterialRepository;
        private readonly Mock<IUserRepository> _userRepository;
        private readonly ISearchStaticMaterialUseCase _searchStaticMaterialUseCase;

        public SearchTests()
        {
            _staticMaterialRepository = new();
            _userRepository = new();

            _searchStaticMaterialUseCase = new SearchStaticMaterialUseCase(
                _staticMaterialRepository.Object,
                _userRepository.Object);
        }

        [Fact(DisplayName = "WhenRequestIsInvalid_ReturnsBadRequest")]
        public async Task Execute_WhenRequestIsInvalid_ReturnsBadRequest()
        {
            _staticMaterialRepository
               .Setup(x => x.SearchAsync(It.IsAny<SearchStaticMaterialRequest>()))
               .ReturnsAsync(SearchStaticMaterialResponseBuilder.CreateDefault());

            var useCaseResponse = await _searchStaticMaterialUseCase
                .Execute(SearchStaticMaterialRequestBuilder
                .CreateDefault()
                .WithRequest(0, 0, -5));

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.BadRequest);
        }

        [Fact(DisplayName = "WhenRequestIsRight_ReturnsOK")]
        public async Task Execute_WhenRequestIsRight_ReturnsOK()
        {
            _staticMaterialRepository
                .Setup(x => x.SearchAsync(It.IsAny<SearchStaticMaterialRequest>()))
                .ReturnsAsync(SearchStaticMaterialResponseBuilder.CreateDefault());

            var useCaseResponse = await _searchStaticMaterialUseCase
                .Execute(SearchStaticMaterialRequestBuilder
                .CreateDefault());

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.OK);
        }
    }
}

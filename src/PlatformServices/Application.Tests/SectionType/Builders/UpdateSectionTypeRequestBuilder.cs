using System;
using System.Collections.Generic;
using Bogus;
using Domain.Enums;
using Model.SectionType._Shared.SectionTypeActivity;
using Model.SectionType.Update.Request;

namespace Application.Tests.SectionType.Builders;

public static class UpdateSectionTypeRequestBuilder
{
    public static UpdateSectionTypeRequest CreateDefault()
    {
        return new Faker<UpdateSectionTypeRequest>()
            .CustomInstantiator(faker => new UpdateSectionTypeRequest
            {
                Id = Guid.NewGuid(),
                Name = faker.Lorem.Word(),
                Activities = new List<SectionTypeActivity>()
                {
                    new() { Index = 0, Activity = Activity.CreateReservoir}
                }
            })
            .Generate();
    }
}

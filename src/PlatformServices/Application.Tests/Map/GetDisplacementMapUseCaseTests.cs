using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Application.Core;
using Application.Maps.GetDisplacementMap;
using Application.Tests.Map.Builders;
using Database.Repositories.Instrument;
using Database.Repositories.Structure;
using Database.Repositories.User;
using FluentAssertions;
using Model.Maps._Shared.DisplacementInstrumentMapData;
using Model.Maps.GetDisplacementMap.Request;
using Moq;
using Xunit;

namespace Application.Tests.Map
{
    [Trait("GetDisplacementMapUseCase", "Execute")]
    public class GetDisplacementMapUseCaseTests
    {
        private readonly Mock<IStructureRepository> _structureRepository;
        private readonly Mock<IInstrumentRepository> _instrumentRepository;
        private readonly Mock<IUserRepository> _userRepository;
        private readonly IGetDisplacementMapUseCase _getDisplacementMapUseCase;

        public GetDisplacementMapUseCaseTests()
        {
            _structureRepository = new();
            _instrumentRepository = new();
            _userRepository = new();

            _getDisplacementMapUseCase = new GetDisplacementMapUseCase(
                _structureRepository.Object,
                _instrumentRepository.Object,
                _userRepository.Object
            );
        }

        [Fact(DisplayName = "WhenRequestIsInvalid_ReturnsBadRequest")]
        public async Task Execute_WhenRequestIsInvalid_ReturnsBadRequest()
        {
            var request = GetDisplacementMapRequestBuilder
                .CreateDefault();

            var useCaseResponse = await _getDisplacementMapUseCase
                .Execute(request);

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.BadRequest);
        }

        [Fact(DisplayName = "WhenRequestIsForbidden_ReturnsForbidden")]
        public async Task Execute_WhenRequestIsForbidden_ReturnsForbidden()
        {
            var request = GetDisplacementMapRequestBuilder
                .CreateDefault()
                .WithInstrument(Guid.NewGuid())
                .WithUserRole(Domain.Enums.Role.Operator);

            _userRepository
                .Setup(repository => repository.GetStructureIdsByUserIdAsync(It.IsAny<Guid>()))
                .ReturnsAsync(Enumerable.Empty<Guid>());

            var useCaseResponse = await _getDisplacementMapUseCase
                .Execute(request);

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.Forbidden);
        }

        [Fact(DisplayName = "WhenAnUnexpectedExceptionIsThrown_ReturnsInternalServerError")]
        public async Task Execute_WhenAnUnexpectedExceptionIsThrown_ReturnsInternalServerError()
        {
            var request = GetDisplacementMapRequestBuilder
                .CreateDefault()
                .WithInstrument(Guid.NewGuid())
                .WithDates(DateTime.UtcNow.AddDays(-1), DateTime.UtcNow);

            _instrumentRepository
                .Setup(repository => repository.GetStructureIdAsync(It.IsAny<Guid>()))
                .Throws(new Exception());

            var useCaseResponse = await _getDisplacementMapUseCase
                .Execute(request);

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.InternalServerError);
        }

        [Fact(DisplayName = "WhenStructureDoesNotHaveInstruments_ReturnsNoContent")]
        public async Task Execute_WhenStructureDoesNotHaveInstruments_ReturnsNoContent()
        {
            var request = GetDisplacementMapRequestBuilder
                .CreateDefault()
                .WithStructure(Guid.NewGuid());

            _instrumentRepository
                .Setup(x => x.GetDisplacementInstrumentsFromSameStructure(It.IsAny<GetDisplacementMapRequest>()))
                .ReturnsAsync(new List<DisplacementInstrumentMapData>());

            var useCaseResponse = await _getDisplacementMapUseCase
                .Execute(request);

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.NoContent);
        }

        [Fact(DisplayName = "WhenRequestIsFilteredByStructure_ReturnsOK")]
        public async Task WhenRequestIsFilteredByStructure_ReturnsOK()
        {
            var request = GetDisplacementMapRequestBuilder
                .CreateDefault()
                .WithStructure(Guid.NewGuid());

            _structureRepository
                .Setup(x => x.GetDisplacementMapData(It.IsAny<Guid>()))
                .ReturnsAsync(DisplacementMapDataStructureBuilder.CreateDefault());

            _instrumentRepository
                .Setup(x => x.GetDisplacementInstrumentsFromSameStructure(It.IsAny<GetDisplacementMapRequest>()))
                .ReturnsAsync(DisplacementInstrumentMapDataBuilder.CreateList());

            var useCaseResponse = await _getDisplacementMapUseCase
                .Execute(request);

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.OK);
        }

        [Fact(DisplayName = "WhenRequestIsFilteredByInstrument_ReturnsOK")]
        public async Task WhenRequestIsFilteredByInstrument_ReturnsOK()
        {
            var request = GetDisplacementMapRequestBuilder
                .CreateDefault()
                .WithInstrument(Guid.NewGuid());

            _instrumentRepository
                .Setup(repository => repository.GetStructureIdAsync(It.IsAny<Guid>()))
                .ReturnsAsync(Guid.NewGuid());

            _structureRepository
                .Setup(x => x.GetDisplacementMapData(It.IsAny<Guid>()))
                .ReturnsAsync(DisplacementMapDataStructureBuilder.CreateDefault());

            _instrumentRepository
                .Setup(x => x.GetDisplacementInstrumentsFromSameStructure(It.IsAny<GetDisplacementMapRequest>()))
                .ReturnsAsync(DisplacementInstrumentMapDataBuilder.CreateList());

            var useCaseResponse = await _getDisplacementMapUseCase
                .Execute(request);

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.OK);
        }
    }
}
using System;
using Application.Extensions;
using Microsoft.AspNetCore.Http;

namespace Application.Tests.Extensions.IFormFileExtensions;

[Trait("IFormFileExtensions", "GetExtension")]
public class GetExtensionsTests
{
    [Fact(DisplayName = "When file name is valid, should return file extension")]
    public void WhenFileNameIsValid_ShouldReturnFileExtension()
    {
        var mockFile = new Mock<IFormFile>();
        mockFile.Setup(f => f.FileName).Returns("example.txt");

        var extension = mockFile.Object.GetExtension();

        extension.Should().Be(".txt");
    }

    [Fact(DisplayName = "When file name has no extension, should return empty string")]
    public void WhenFileNameHasNoExtension_ShouldReturnEmptyString()
    {
        var mockFile = new Mock<IFormFile>();
        mockFile.Setup(f => f.FileName).Returns("example");

        var extension = mockFile.Object.GetExtension();

        extension.Should().Be(string.Empty);
    }

    [Fact(DisplayName = "When file name is null, should return empty string")]
    public void WhenFileIsNull_ShouldThrowArgumentNullException_()
    {
        IFormFile file = null;

        Action act = () => file.GetExtension();

        act.Should().Throw<ArgumentNullException>();
    }
}
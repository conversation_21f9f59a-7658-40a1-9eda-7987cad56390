using Application.ActionPlan.Update;
using Application.Core;
using Application.Services.BlobStorage;
using Application.Tests.ActionPlan.Builders;
using Database.Repositories.ActionPlan;
using Database.Repositories.User;
using Microsoft.Extensions.Options;
using Model.ActionPlan.Update.Request;
using Moq;
using System;
using System.Threading.Tasks;

namespace Application.Tests.ActionPlan
{
    [Trait("UpdateActionPlanUseCase", "Execute")]
    public class UpdateTests
    {
        private readonly Mock<IActionPlanRepository> _actionPlanRepository;
        private readonly Mock<IUserRepository> _userRepository;
        private readonly IUpdateActionPlanUseCase _updateActionPlanUseCase;
        private readonly Mock<IBlobStorageService> _blobService;
        private readonly Mock<IOptions<BlobStorageOptions>> _blobOptions;

        public UpdateTests()
        {
            _actionPlanRepository = new();
            _userRepository = new();
            _blobService = new();
            _blobOptions = new();

            _blobOptions
               .Setup(o => o.Value)
               .Returns(new BlobStorageOptions
               {
                   AccountKey = "AccountKey",
                   AccountName = "AccountName",
                   ConnectionString = "ConnectionString",
                   ClientsContainer = "ContainerName"
               });

            _updateActionPlanUseCase = new UpdateActionPlanUseCase(
                _blobService.Object,
                _blobOptions.Object,
                _userRepository.Object,
                _actionPlanRepository.Object);
        }

        [Fact(DisplayName = "When request is null, returns bad request")]
        public async Task WhenRequestIsNull_ReturnsBadRequest()
        {
            var response = await _updateActionPlanUseCase.Execute(null);

            response.Status.Should().Be(UseCaseResponseKind.BadRequest);
        }

        [Fact(DisplayName = "When request is invalid, returns bad request")]
        public async Task WhenRequestIsInvalid_ReturnsBadRequest()
        {
            var request = new UpdateActionPlanRequest();

            var response = await _updateActionPlanUseCase.Execute(request);

            response.Status.Should().Be(UseCaseResponseKind.BadRequest);
        }

        [Fact(DisplayName = "When action plan is not found, returns bad request")]
        public async Task WhenActionPlanIsNotFound_ReturnsBadRequest()
        {
            var request = UpdateActionPlanRequestBuilder.CreateDefault();

            _actionPlanRepository
                .Setup(r => r.GetAsync(It.IsAny<Guid>()))
                .ReturnsAsync((Domain.Entities.ActionPlan)null);

            var response = await _updateActionPlanUseCase.Execute(request);

            response.Status.Should().Be(UseCaseResponseKind.BadRequest);
        }

        [Fact(DisplayName = "When an exception is thrown, returns internal server error")]
        public async Task WhenAnExceptionIsThrown_ReturnsInternalServerError()
        {
            var request = UpdateActionPlanRequestBuilder.CreateDefault();

            _actionPlanRepository
                .Setup(r => r.GetAsync(It.IsAny<Guid>()))
                .ThrowsAsync(new Exception());

            var response = await _updateActionPlanUseCase.Execute(request);

            response.Status.Should().Be(UseCaseResponseKind.InternalServerError);
        }

        [Fact(DisplayName = "When action plan completion date has value, returns bad request")]
        public async Task WhenActionPlanCompletionDateHasValue_ReturnsBadRequest()
        {
            var request = UpdateActionPlanRequestBuilder.CreateDefault();

            var actionPlan = ActionPlanBuilder.CreateDefault();

            actionPlan.CompletionDate = DateTime.Now;

            _actionPlanRepository
                .Setup(r => r.GetAsync(It.IsAny<Guid>()))
                .ReturnsAsync(actionPlan);

            var response = await _updateActionPlanUseCase.Execute(request);

            response.Status.Should().Be(UseCaseResponseKind.BadRequest);
        }

        [Fact(DisplayName = "When request is OK, returns OK")]
        public async Task WhenRequestIsOK_ReturnsOK()
        {
            var request = UpdateActionPlanRequestBuilder.CreateDefault();

            var actionPlan = ActionPlanBuilder.CreateDefault();

            _actionPlanRepository
                .Setup(r => r.GetAsync(It.IsAny<Guid>()))
                .ReturnsAsync(actionPlan);

            var response = await _updateActionPlanUseCase.Execute(request);

            response.Status.Should().Be(UseCaseResponseKind.OK);
        }
    }
}

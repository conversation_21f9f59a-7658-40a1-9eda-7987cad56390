using Model.Section.List.Response;
using System;
using System.Collections.Generic;

namespace Application.Tests.Section.Builders
{
    public static class ListSectionResponseBuilder
    {
        public static IEnumerable<ListSectionResponse> CreateDefault()
        {
            return new List<ListSectionResponse>
            {
                new()
                {
                    Id = Guid.NewGuid(),
                    Name = "Section"
                }
            };
        }
    }
}

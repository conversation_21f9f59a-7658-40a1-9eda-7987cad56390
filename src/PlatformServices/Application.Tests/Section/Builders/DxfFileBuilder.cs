using System;
using IxMilia.Dxf;
using IxMilia.Dxf.Entities;
using System.Collections.Generic;
using System.IO;

namespace Application.Tests.Section.Builders
{
    public static class DxfFileBuilder
    {
        public static DxfFile CreateDefault()
        {
            var dxfFile = new DxfFile();

            var vertices = new List<DxfVertex>()
            {
                new(new DxfPoint(100.707634860977, 0, 0)),
                new(new DxfPoint(100.707634860977, 110.452716498892, 0)),
                new(new DxfPoint(500.707634860686, 110.452716498892, 0)),
                new(new DxfPoint(500.707634860686, 0, 0))
            };

            dxfFile.Entities.Add(new DxfPolyline(vertices){Layer = "external"});

            return dxfFile;
        }

        public static DxfFile WithPolyline(
            this DxfFile dxfFile,
            List<DxfVertex> vertices,
            string layer = "")
        {
            dxfFile.Entities.Clear();
            
            var polyline = new DxfPolyline(vertices);

            if (!string.IsNullOrWhiteSpace(layer))
            {
                polyline.Layer = layer;
            }
            
            dxfFile.Entities.Add(polyline);

            return dxfFile;
        }

        public static DxfFile WithPolyline(
            this DxfFile dxfFile,
            DxfPoint start, 
            DxfPoint end,
            string layer = "")
        {
            dxfFile.Entities.Clear();
            
            var vertices = new List<DxfVertex>()
            {
                new(start),
                new(end)
            };
            
            var polyline = new DxfPolyline(vertices);

            if (!string.IsNullOrWhiteSpace(layer))
            {
                polyline.Layer = layer;
            }
            
            dxfFile.Entities.Add(polyline);

            return dxfFile;
        }

        public static DxfFile WithLayer(this DxfFile dxfFile, string name)
        {
            dxfFile.Layers.Add(new DxfLayer(name));
            return dxfFile;
        }
        
        public static string AsBase64(this DxfFile dxfFile)
        {
            using var memoryStream = new MemoryStream();
            dxfFile.Save(memoryStream);
            return Convert.ToBase64String(memoryStream.ToArray());
        }
    }
}

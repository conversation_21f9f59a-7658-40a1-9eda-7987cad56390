using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Application.Core;
using Application.Section.GetReviewFile;
using Application.Services.BlobStorage;
using Application.Tests.Instrument.Builders;
using Application.Tests.Section.Builders;
using Database.Repositories.Instrument;
using Database.Repositories.Section;
using Database.Repositories.User;
using Dxf.Core;
using Geometry.Core;
using IxMilia.Dxf;
using IxMilia.Dxf.Entities;
using Microsoft.Extensions.Options;
using DxfPoint = IxMilia.Dxf.DxfPoint;

namespace Application.Tests.Section.GetSectionReviewFile
{
    [Trait("GetSectionReviewFileUseCase", "Execute")]
    public class ExecuteTests
    {
        private readonly Mock<ISectionRepository> _sectionRepository;
        private readonly Mock<IInstrumentRepository> _instrumentRepository;
        private readonly Mock<IUserRepository> _userRepository;
        private readonly IGetSectionReviewFileUseCase _getSectionReviewFileUseCase;
        private readonly Mock<IBlobStorageService> _blobService;
        private readonly Mock<IOptions<BlobStorageOptions>> _blobOptions;

        public ExecuteTests()
        {
            _sectionRepository = new();
            _instrumentRepository = new();
            _userRepository = new();
            _blobService = new();
            _blobOptions = new();

            _blobOptions
                .Setup(o => o.Value)
                .Returns(new BlobStorageOptions
                {
                    AccountKey = "AccountKey",
                    AccountName = "AccountName",
                    ConnectionString = "ConnectionString",
                    ClientsContainer = "ContainerName"
                });

            _getSectionReviewFileUseCase = new GetSectionReviewFileUseCase(
                _sectionRepository.Object,
                _userRepository.Object,
                _blobService.Object,
                _blobOptions.Object);
        }

        [Fact(DisplayName = "WhenRequestIsInvalid_ReturnsBadRequest")]
        public async Task Execute_WhenRequestIsInvalid_ReturnsBadRequest()
        {
            _sectionRepository
                .Setup(x => x.GetAsync(It.IsAny<Guid>()))
                .ReturnsAsync(default(Domain.Entities.Section));

            var useCaseResponse = await _getSectionReviewFileUseCase
                .Execute(GetSectionReviewFileRequestBuilder
                .CreateDefault()
                .WithRequest(Guid.NewGuid(), Guid.Empty));

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.BadRequest);
        }

        [Fact(DisplayName = "WhenAnUnexpectedExceptionIsThrown_ReturnsInternalServerError")]
        public async Task Execute_WhenAnUnexpectedExceptionIsThrown_ReturnsInternalServerError()
        {
            _sectionRepository
                .Setup(x => x.GetAsync(It.IsAny<Guid>()))
                .Throws(new Exception());

            var useCaseResponse = await _getSectionReviewFileUseCase
                .Execute(GetSectionReviewFileRequestBuilder
                .CreateDefault());

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.InternalServerError);
        }

        [Fact(DisplayName = "WhenSectionDoesNotExist_ReturnsNoContent")]
        public async Task Execute_WhenSectionDoesNotExist_ReturnsNoContent()
        {
            _sectionRepository
                .Setup(x => x.GetAsync(It.IsAny<Guid>()))
                .ReturnsAsync(default(Domain.Entities.Section));

            var useCaseResponse = await _getSectionReviewFileUseCase
                .Execute(GetSectionReviewFileRequestBuilder
                .CreateDefault());

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.NoContent);
        }

        [Fact(DisplayName = "WhenSectionReviewHasNoFile_ReturnsBadRequest")]
        public async Task Execute_WhenSectionHasNoReviewFile_ReturnsNoContent()
        {
            var section = SectionBuilder
                .CreateDefault();

            section.Reviews.ForEach(x => x.Drawing = null);

            _sectionRepository
                .Setup(x => x.GetAsync(It.IsAny<Guid>()))
                .ReturnsAsync(section);

            var useCaseResponse = await _getSectionReviewFileUseCase
                .Execute(GetSectionReviewFileRequestBuilder
                .CreateDefault());

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.BadRequest);
        }

        [Fact(DisplayName = "WhenSectionHasNoInstrument_ReturnsBadRequest")]
        public async Task Execute_WhenSectionHasNoInstrument_ReturnsBadRequest()
        {
            var section = SectionBuilder
               .CreateDefault()
               .WithReview(2, new()
               {
                   Name = "1.dxf",
                   UniqueName = Guid.NewGuid().ToString("N")
               });

            section.Instruments.RemoveAll(x => true);

            _sectionRepository
                .Setup(x => x.GetAsync(It.IsAny<Guid>()))
                .ReturnsAsync(section);

            var useCaseResponse = await _getSectionReviewFileUseCase
                .Execute(GetSectionReviewFileRequestBuilder
                .CreateDefault());

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.BadRequest);
        }

        [Fact(DisplayName = "WhenRequestIsRight_ReturnsOK")]
        public async Task Execute_WhenRequestIsRight_ReturnsOK()
        {
            var instrument = InstrumentBuilder.CreateWaterLevelIndicator();

            var section = SectionBuilder
               .CreateDefault()
               .WithReview(2, new()
               {
                   Name = "test.dxf",
                   UniqueName = Guid.NewGuid().ToString("N")
               })
               .WithInstrument(instrument);

            _instrumentRepository.Setup(x => x.GetAsync(It.IsAny<Guid>()))
                .ReturnsAsync(instrument);

            var dxfFile = DxfFileBuilder.CreateDefault();

            using var memoryStream = new MemoryStream();

            dxfFile.Save(memoryStream);

            _blobService
                .Setup(x => x.GetAsync(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(memoryStream.ToArray());

            _sectionRepository
                .Setup(x => x.GetAsync(It.IsAny<Guid>()))
                .ReturnsAsync(section);

            var useCaseResponse = await _getSectionReviewFileUseCase
                .Execute(GetSectionReviewFileRequestBuilder
                .CreateDefault());

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.OK);
        }
    }
}

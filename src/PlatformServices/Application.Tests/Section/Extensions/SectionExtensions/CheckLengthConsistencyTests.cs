using Application.Section.Extensions;
using Application.Tests.Section.Builders;
using IxMilia.Dxf.Entities;
using DxfPoint = IxMilia.Dxf.DxfPoint;
using File = Domain.ValueObjects.File;

namespace Application.Tests.Section.Extensions.SectionExtensions;

[Trait("SectionExtensions", "CheckLengthConsistency`")]
public class CheckLengthConsistencyTests
{
    [Fact(DisplayName = "When drawing is null, then returns consistent SectionLength")]
    public void WhenDrawingIsNull_ReturnsConsistentSectionLength()
    {
        var section = new Domain.Entities.Section();

        var result = section.CheckLengthConsistency(null);

        result.Should().NotBeNull();
    }
    
    [Fact(DisplayName = "When drawing is null, then returns DxfLengthInMeters as 0")]
    public void WhenDrawingIsNull_ReturnsDxfLengthMetersAs0()
    {
        var section = new Domain.Entities.Section();

        var result = section.CheckLengthConsistency(null);

        result.DxfLengthInMeters.Should().Be(0);
    }
    
    [Fact(DisplayName = "When drawing is null, then returns CoordinatesLengthInMeters as 0")]
    public void WhenDrawingIsNull_ReturnsCoordinatesLengthInMetersAs0()
    {
        var section = new Domain.Entities.Section();

        var result = section.CheckLengthConsistency(null);

        result.CoordinatesLengthInMeters.Should().Be(0);
    }
    
    [Fact(DisplayName = "When drawing is null, then returns DifferenceInMeters as 0")]
    public void WhenDrawingIsNull_ReturnsDifferenceInMetersAs0()
    {
        var section = new Domain.Entities.Section();

        var result = section.CheckLengthConsistency(null);

        result.DifferenceInMeters.Should().Be(0);
    }
    
    [Fact(DisplayName = "When drawing is null, then returns IsConsistent as true")]
    public void WhenDrawingIsNull_ReturnsIsConsistentAsTrue()
    {
        var section = new Domain.Entities.Section();

        var result = section.CheckLengthConsistency(null);

        result.IsConsistent.Should().BeTrue();
    }

    [Fact(DisplayName = "When drawing has consistent length, then returns consistent SectionLength")]
    public void WhenDrawingHasConsistentLength_ReturnsConsistentSectionLength()
    {
        var section = SectionBuilder.CreateDefault();
        var base64 = DxfFileBuilder.CreateDefault().AsBase64();
        var file = new File()
        {
            Base64 = base64,
        };

        var result = section.CheckLengthConsistency(file);

        result.Should().NotBeNull();
    }
    
    [Fact(DisplayName = "When drawing has consistent length, then returns consistent DxfLengthInMeters")]
    public void WhenDrawingHasConsistentLength_ReturnsConsistentDxfLengthInMeters()
    {
        var section = SectionBuilder.CreateDefault();
        var base64 = DxfFileBuilder.CreateDefault().AsBase64();
        var file = new File()
        {
            Base64 = base64,
        };
        
        const double expectedLength = 400;

        var result = section.CheckLengthConsistency(file);

        result.DxfLengthInMeters.Should().Be(expectedLength);
    }
    
    [Fact(DisplayName = "When drawing has consistent length, then returns consistent CoordinatesLengthInMeters")]
    public void WhenDrawingHasConsistentLength_ReturnsConsistentCoordinatesLengthInMeters()
    {
        var section = SectionBuilder.CreateDefault();
        var base64 = DxfFileBuilder.CreateDefault().AsBase64();
        var file = new File()
        {
            Base64 = base64,
        };

        const double expectedLength = 399.74;

        var result = section.CheckLengthConsistency(file);

        result.CoordinatesLengthInMeters.Should().Be(expectedLength);
    }
    
    [Fact(DisplayName = "When drawing has consistent length, then returns consistent DifferenceInMeters")]
    public void WhenDrawingHasConsistentLength_ReturnsConsistentDifferenceInMeters()
    {
        var section = SectionBuilder.CreateDefault();
        var base64 = DxfFileBuilder.CreateDefault().AsBase64();
        var file = new File()
        {
            Base64 = base64,
        };

        var result = section.CheckLengthConsistency(file);
        
        const double expectedDifference = 0;
        const double acceptableTolerance = 1;

        result.DifferenceInMeters
            .Should()
            .BeApproximately(expectedDifference, acceptableTolerance);
    }
    
    [Fact(DisplayName = "When drawing has consistent length, then returns IsConsistent as true")]
    public void WhenDrawingHasConsistentLength_ReturnsIsConsistentAsTrue()
    {
        var section = SectionBuilder.CreateDefault();
        var base64 = DxfFileBuilder.CreateDefault().AsBase64();
        var file = new File()
        {
            Base64 = base64,
        };

        var result = section.CheckLengthConsistency(file);

        result.IsConsistent.Should().BeTrue();
    }

    [Fact(DisplayName = "When drawing has inconsistent length, then returns inconsistent SectionLength")]
    public void WhenDrawingHasInconsistentLength_ReturnsInconsistentSectionLength()
    {
        var section = SectionBuilder.CreateDefault();
            
        var vertices = new System.Collections.Generic.List<DxfVertex>()
        {
            new (new DxfPoint(0, 0, 0)),
            new (new DxfPoint(0, 15, 0)),
            new (new DxfPoint(600, 10, 0)),
            new (new DxfPoint(600, 0, 0)),
        };

        var base64 = DxfFileBuilder
            .CreateDefault()
            .WithPolyline(vertices, "external")
            .AsBase64();
        
        var file = new File()
        {
            Base64 = base64,
        };

        var result = section.CheckLengthConsistency(file);

        result.Should().NotBeNull();
    }
    
    [Fact(DisplayName = "When drawing has inconsistent length, then returns DxfLengthInMeters")]
    public void WhenDrawingHasInconsistentLength_ReturnsDxfLengthInMeters()
    {
        var section = SectionBuilder.CreateDefault();

        var vertices = new System.Collections.Generic.List<DxfVertex>()
        {
            new (new DxfPoint(0, 0, 0)),
            new (new DxfPoint(0, 15, 0)),
            new (new DxfPoint(600, 10, 0)),
            new (new DxfPoint(600, 0, 0)),
        };

        var base64 = DxfFileBuilder
            .CreateDefault()
            .WithPolyline(vertices, "external")
            .AsBase64();

        var file = new File()
        {
            Base64 = base64,
        };
        
        const double expectedLength = 600;

        var result = section.CheckLengthConsistency(file);

        result.DxfLengthInMeters.Should().Be(expectedLength);
    }
    
    [Fact(DisplayName = "When drawing has inconsistent length, then returns CoordinatesLengthInMeters")]
    public void WhenDrawingHasInconsistentLength_ReturnsCoordinatesLengthInMeters()
    {
        var section = SectionBuilder.CreateDefault();

        var vertices = new System.Collections.Generic.List<DxfVertex>()
        {
            new (new DxfPoint(0, 0, 0)),
            new (new DxfPoint(0, 15, 0)),
            new (new DxfPoint(600, 10, 0)),
            new (new DxfPoint(600, 0, 0)),
        };

        var base64 = DxfFileBuilder
            .CreateDefault()
            .WithPolyline(vertices, "external")
            .AsBase64();

        var file = new File()
        {
            Base64 = base64,
        };

        const double expectedLength = 399.74;

        var result = section.CheckLengthConsistency(file);

        result.CoordinatesLengthInMeters.Should().Be(expectedLength);
    }
    
    [Fact(DisplayName = "When drawing has inconsistent length, then returns DifferenceInMeters")]
    public void WhenDrawingHasInconsistentLength_ReturnsDifferenceInMeters()
    {
        var section = SectionBuilder.CreateDefault();

        var vertices = new System.Collections.Generic.List<DxfVertex>()
        {
            new (new DxfPoint(0, 0, 0)),
            new (new DxfPoint(0, 15, 0)),
            new (new DxfPoint(600, 10, 0)),
            new (new DxfPoint(600, 0, 0)),
        };

        var base64 = DxfFileBuilder
            .CreateDefault()
            .WithPolyline(vertices, "external")
            .AsBase64();

        var file = new File()
        {
            Base64 = base64,
        };
        
        var result = section.CheckLengthConsistency(file);

        result.DifferenceInMeters
            .Should()
            .BeGreaterThan(Domain.Entities.Section.AcceptableLengthDifferenceInMeters);
    }
    
    [Fact(DisplayName = "When drawing has inconsistent length, then returns IsConsistent as false")]
    public void WhenDrawingHasInconsistentLength_ReturnsIsConsistentAsFalse()
    {
        var section = SectionBuilder.CreateDefault();

        var vertices = new System.Collections.Generic.List<DxfVertex>()
        {
            new (new DxfPoint(0, 0, 0)),
            new (new DxfPoint(0, 15, 0)),
            new (new DxfPoint(600, 10, 0)),
            new (new DxfPoint(600, 0, 0)),
        };

        var base64 = DxfFileBuilder
            .CreateDefault()
            .WithPolyline(vertices, "external")
            .AsBase64();

        var file = new File()
        {
            Base64 = base64,
        };

        var result = section.CheckLengthConsistency(file);

        result.IsConsistent.Should().BeFalse();
    }
}
using Application.Core;
using Application.Image.Delete;
using Application.Services.BlobStorage;
using Application.Tests.Image.Builders;
using Database.Repositories.Instrument;
using Database.Repositories.InstrumentImage;
using Database.Repositories.StructureImage;
using Database.Repositories.User;
using Microsoft.Extensions.Options;
using Model.Image.Delete.Request;
using Moq;
using System;
using System.Threading.Tasks;
using Xunit;

namespace Application.Tests.Image
{
    [Trait("DeleteImageUseCase", "Execute")]
    public class DeleteTests
    {
        private readonly Mock<IStructureImageRepository> _structureImageRepository;
        private readonly Mock<IInstrumentImageRepository> _instrumentImageRepository;
        private readonly Mock<IInstrumentRepository> _instrumentRepository;
        private readonly Mock<IUserRepository> _userRepository;
        private readonly Mock<IBlobStorageService> _blobService;
        private readonly Mock<IOptions<BlobStorageOptions>> _blobOptions;

        private readonly IDeleteImageUseCase _useCase;

        public DeleteTests()
        {
            _blobService = new();
            _blobOptions = new();

            _blobOptions
             .Setup(o => o.Value)
             .Returns(new BlobStorageOptions
             {
                 AccountKey = "AccountKey",
                 AccountName = "AccountName",
                 ConnectionString = "ConnectionString",
                 ClientsContainer = "ContainerName"
             });

            _structureImageRepository = new();
            _instrumentImageRepository = new();
            _instrumentRepository = new();
            _userRepository = new();

            _useCase = new DeleteImageUseCase(_userRepository.Object,
                _instrumentImageRepository.Object,
                _structureImageRepository.Object,
                _instrumentRepository.Object,
                _blobService.Object,
                _blobOptions.Object);
        }

        [Fact(DisplayName = "WhenRequestIsInvalid_ReturnsBadRequest")]
        public async void Execute_WhenRequestIsInvalid_ReturnsBadRequest()
        {
            var request = DeleteImageRequestBuilder
                .DeleteImage()
                .RemoveImageId();

            var useCaseResponse = await _useCase.Execute(request);

            Assert.Equal(UseCaseResponseKind.BadRequest, useCaseResponse.Status);
        }

        [Fact(DisplayName = "WhenRequesIsValid_ReturnsOk")]
        public async Task WhenRequesIsValidReturnsOk()
        {
            _structureImageRepository
                .Setup(x => x.GetAsync(It.IsAny<Guid>()))
                .ReturnsAsync(DeleteImageRequestBuilder.CreateDefault());
            _structureImageRepository
                .Setup(x => x.DeleteAsync(It.IsAny<DeleteImageRequest>()))
                .ReturnsAsync(1);

            var request = DeleteImageRequestBuilder
                .DeleteImage();

            var useCaseResponse = await _useCase.Execute(request);

            Assert.Equal(UseCaseResponseKind.OK, useCaseResponse.Status);
        }
    }
}

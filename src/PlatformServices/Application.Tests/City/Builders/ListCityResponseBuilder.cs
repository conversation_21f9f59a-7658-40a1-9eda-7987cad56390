using Model.City.List.Response;
using System;
using System.Collections.Generic;

namespace Application.Tests.City.Builders
{
    public static class ListCityResponseBuilder
    {
        public static IEnumerable<ListCityResponse> CreateDefault()
        {
            return new List<ListCityResponse>
            {
                new ListCityResponse
                {
                    Id = Guid.NewGuid(),
                    Name = "São Paulo"
                }
            };
        }
    }
}

using Bogus;
using Dxf.Core.Extensions;

namespace Dxf.Core.Tests.Extensions.StringExtensions;

[<PERSON><PERSON><PERSON>("StringExtensions", "ContainsLimitsKeyword")]
public class ContainsLimitsKeywordTests
{
    [Theory(DisplayName =
        "When input contains limits keyword, then return true")]
    [InlineData("limits")]
    [InlineData("LIMITS")]
    [InlineData("limites")]
    [InlineData("LIMITES")]
    [InlineData("slope_limits")]
    [InlineData("SLOPE_LIMITS")]
    [InlineData("slopelimits")]
    [InlineData("SLOPELIMITS")]
    [InlineData("slope-limits")]
    [InlineData("SLOPE-LIMITS")]
    public void WhenInputContainsLimitsKeyword_ThenReturnTrue(string input)
    {
        var result = input.ContainsLimitsKeyword();

        result.Should().BeTrue();
    }

    [Fact(DisplayName =
        "When input does not contain limits keyword, then return false")]
    public void WhenInputDoesNotContainsLimitsKeyword_ThenReturnFalse()
    {
        var input = new Faker().Lorem.Sentence();

        var result = input.ContainsLimitsKeyword();

        result.Should().BeFalse();
    }
}
using IxMilia.Dxf;

namespace Dxf.Core.Tests.DxfFileExtensions;

[<PERSON>rai<PERSON>("DxfFileExtensions", "HasLayers")]
public class HasLayersTests
{
    [Fact(DisplayName = "When DxfFile contains layers, then return true")]
    public void WhenDxfFileContainsLayers_ThenReturnTrue()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("Layer1"));

        var result = dxfFile.HasLayers();

        result.Should().BeTrue();
    }
}
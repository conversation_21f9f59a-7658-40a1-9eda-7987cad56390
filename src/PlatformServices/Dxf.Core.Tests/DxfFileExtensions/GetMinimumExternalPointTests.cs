using IxMilia.Dxf;
using IxMilia.Dxf.Entities;

namespace Dxf.Core.Tests.DxfFileExtensions;

[Trait("DxfFileExtensions", "GetMinimumExternalPoint")]
public class GetMinimumExternalPointTests
{
    [Fact(DisplayName = "When DxfFile contains line entities in the external layer, then return the minimum X point")]
    public void WhenDxfFileContainsLineEntitiesInExternalLayer_ThenReturnMinimumXPoint()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("external"));
        dxfFile.Entities.Add(new DxfLine
        {
            Layer = "external",
            P1 = new IxMilia.Dxf.DxfPoint(0, 2, 0),
            P2 = new IxMilia.Dxf.DxfPoint(3, 4, 0)
        });
        
        const double expectedX = 0.0;

        var result = dxfFile.GetMinimumExternalPoint();

        result.X.Should().Be(expectedX);
    }
    
    [Fact(DisplayName = "When DxfFile contains line entities in the external layer, then return the minimum Y point")]
    public void WhenDxfFileContainsLineEntitiesInExternalLayer_ThenReturnMinimumYPoint()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("external"));
        dxfFile.Entities.Add(new DxfLine
        {
            Layer = "external",
            P1 = new IxMilia.Dxf.DxfPoint(0, 2, 0),
            P2 = new IxMilia.Dxf.DxfPoint(3, 4, 0)
        });
        
        const double expectedY = 2.0;

        var result = dxfFile.GetMinimumExternalPoint();

        result.Y.Should().Be(expectedY);
    }
    
    [Fact(DisplayName = "When DxfFile contains polyline entities in the external layer, then return the minimum X point")]
    public void WhenDxfFileContainsPolyLineEntitiesInExternalLayer_ThenReturnMinimumXPoint()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("external"));
        dxfFile.Entities.Add(new DxfPolyline(new List<DxfVertex>()
        {
            new (new IxMilia.Dxf.DxfPoint(0, 0, 0)),
            new (new IxMilia.Dxf.DxfPoint(0, 1, 0)),
            new (new IxMilia.Dxf.DxfPoint(5, 2, 0)),
            new (new IxMilia.Dxf.DxfPoint(10, 1, 0)),
            new (new IxMilia.Dxf.DxfPoint(10, 0, 0))
        })
        {
            Layer = "external"
        });
        
        const double expectedX = 0.0;

        var result = dxfFile.GetMinimumExternalPoint();

        result.X.Should().Be(expectedX);
    }
    
    [Fact(DisplayName = "When DxfFile contains polyline entities in the external layer, then return the minimum Y point")]
    public void WhenDxfFileContainsPolyLineEntitiesInExternalLayer_ThenReturnMinimumYPoint()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("external"));
        dxfFile.Entities.Add(new DxfPolyline(new List<DxfVertex>()
        {
            new (new IxMilia.Dxf.DxfPoint(0, 0, 0)),
            new (new IxMilia.Dxf.DxfPoint(0, 1, 0)),
            new (new IxMilia.Dxf.DxfPoint(5, 2, 0)),
            new (new IxMilia.Dxf.DxfPoint(10, 1, 0)),
            new (new IxMilia.Dxf.DxfPoint(10, 0, 0))
        })
        {
            Layer = "external"
        });
        
        const double expectedY = 0.0;

        var result = dxfFile.GetMinimumExternalPoint();

        result.Y.Should().Be(expectedY);
    }

    [Fact(DisplayName = "When DxfFile contains no entities in the external layer, then throw exception")]
    public void WhenDxfFileContainsNoEntitiesInExternalLayer_ThenThrowException()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("external"));
        dxfFile.Entities.Add(new DxfLine
        {
            Layer = "otherLayer",
            P1 = new IxMilia.Dxf.DxfPoint(1, 2, 0),
            P2 = new IxMilia.Dxf.DxfPoint(3, 4, 0)
        });

        Assert.Throws<InvalidOperationException>(() => dxfFile.GetMinimumExternalPoint());
    }

    [Fact(DisplayName = "When DxfFile contains no entities, then throw exception")]
    public void WhenDxfFileContainsNoEntities_ThenThrowException()
    {
        var dxfFile = new DxfFile();

        Assert.Throws<InvalidOperationException>(() => dxfFile.GetMinimumExternalPoint());
    }
}
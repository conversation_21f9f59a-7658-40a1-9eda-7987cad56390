using IxMilia.Dxf;

namespace Dxf.Core.Tests.DxfFileExtensions;

[Trait("DxfFileExtensions", "HasExternalLayer")]
public class HasExternalLayerTests
{
    [Fact(DisplayName = "When DxfFile contains an external layer, then return true")]
    public void WhenDxfFileContainsExternalLayer_ThenReturnTrue()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("external"));
        
        var result = dxfFile.HasExternalLayer();

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When DxfFile does not contain an external layer, then return false")]
    public void WhenDxfFileDoesNotContainExternalLayer_ThenReturnFalse()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("otherLayer"));

        var result = dxfFile.HasExternalLayer();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When DxfFile contains no layers, then return false")]
    public void WhenDxfFileContainsNoLayers_ThenReturnFalse()
    {
        var dxfFile = new DxfFile();

        var result = dxfFile.HasExternalLayer();

        result.Should().BeFalse();
    }
}
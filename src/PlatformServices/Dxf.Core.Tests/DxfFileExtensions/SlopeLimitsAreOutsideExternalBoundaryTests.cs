using IxMilia.Dxf;
using IxMilia.Dxf.Entities;

namespace Dxf.Core.Tests.DxfFileExtensions;

[Trait("DxfFileExtensions", "SlopeLimitsAreOutsideExternalBoundary")]
public class SlopeLimitsAreOutsideExternalBoundaryTests
{
    [Fact(DisplayName = "When slope limits circles are outside external boundary, then return true")]
    public void WhenSlopeLimitsCirclesAreOutsideExternalBoundary_ThenReturnTrue()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("external"));
        dxfFile.Entities.Add(new DxfLine
        {
            Layer = "external",
            P1 = new IxMilia.Dxf.DxfPoint(1, 0, 0),
            P2 = new IxMilia.Dxf.DxfPoint(5, 0, 0)
        });
        dxfFile.Entities.Add(new DxfCircle
        {
            Layer = "slope_limits",
            Center = new IxMilia.Dxf.DxfPoint(0, 0, 0),
            Radius = 1
        });

        var result = dxfFile.SlopeLimitsAreOutsideExternalBoundary();

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When slope limits circles are within external boundary, then return false")]
    public void WhenSlopeLimitsCirclesAreWithinExternalBoundary_ThenReturnFalse()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("external"));
        dxfFile.Entities.Add(new DxfLine
        {
            Layer = "external",
            P1 = new IxMilia.Dxf.DxfPoint(1, 0, 0),
            P2 = new IxMilia.Dxf.DxfPoint(5, 0, 0)
        });
        dxfFile.Entities.Add(new DxfCircle
        {
            Layer = "slope_limits",
            Center = new IxMilia.Dxf.DxfPoint(3, 0, 0),
            Radius = 1
        });

        var result = dxfFile.SlopeLimitsAreOutsideExternalBoundary();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When no slope limits circles exist, then return false")]
    public void WhenNoSlopeLimitsCirclesExist_ThenReturnFalse()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("external"));
        dxfFile.Entities.Add(new DxfLine
        {
            Layer = "external",
            P1 = new IxMilia.Dxf.DxfPoint(1, 0, 0),
            P2 = new IxMilia.Dxf.DxfPoint(5, 0, 0)
        });

        var result = dxfFile.SlopeLimitsAreOutsideExternalBoundary();

        result.Should().BeFalse();
    }
}
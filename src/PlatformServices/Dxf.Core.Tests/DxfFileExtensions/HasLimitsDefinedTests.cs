using IxMilia.Dxf;
using IxMilia.Dxf.Entities;

namespace Dxf.Core.Tests.DxfFileExtensions;

[Trait("DxfFileExtensions", "HasLimitsDefined")]
public class HasLimitsDefinedTests
{
    [Fact(DisplayName = "When DxfFile contains limits layer and entity, then return true")]
    public void WhenDxfFileContainsLimitsLayerAndEntity_ThenReturnTrue()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("limits"));
        dxfFile.Entities.Add(new DxfLine { Layer = "limits" });

        var result = dxfFile.HasLimitsDefined();

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When DxfFile contains limits entity but no layer, then return false")]
    public void WhenDxfFileContainsLimitsEntityButNoLayer_ThenReturnFalse()
    {
        var dxfFile = new DxfFile();
        dxfFile.Entities.Add(new DxfLine { Layer = "limits" });

        var result = dxfFile.HasLimitsDefined();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When DxfFile contains limits layer but no entity, then return false")]
    public void WhenDxfFileContainsLimitsLayerButNoEntity_ThenReturnFalse()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("limits"));

        var result = dxfFile.HasLimitsDefined();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When DxfFile does not contain limits layer or entity, then return false")]
    public void WhenDxfFileDoesNotContainLimitsLayerOrEntity_ThenReturnFalse()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("otherLayer"));
        dxfFile.Entities.Add(new DxfLine { Layer = "otherLayer" });

        var result = dxfFile.HasLimitsDefined();

        result.Should().BeFalse();
    }
}
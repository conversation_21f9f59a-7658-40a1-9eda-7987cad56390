using IxMilia.Dxf;
using IxMilia.Dxf.Entities;

namespace Dxf.Core.Tests.DxfFileExtensions;

[Trait("DxfFileExtensions", "GetMaximumExternalPoint")]
public class GetMaximumExternalPointTests
{
    [Fact(DisplayName = "When DxfFile contains line entities in the external layer, then return the maximum X point")]
    public void WhenDxfFileContainsLineEntitiesInExternalLayer_ThenReturnMaximumXPoint()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("external"));
        dxfFile.Entities.Add(new DxfLine
        {
            Layer = "external",
            P1 = new IxMilia.Dxf.DxfPoint(0, 2, 0),
            P2 = new IxMilia.Dxf.DxfPoint(3, 4, 0)
        });
        
        const double expectedX = 3.0;

        var result = dxfFile.GetMaximumExternalPoint();

        result.X.Should().Be(expectedX);
    }
    
    [Fact(DisplayName = "When DxfFile contains line entities in the external layer, then return the maximum Y point")]
    public void WhenDxfFileContainsLineEntitiesInExternalLayer_ThenReturnMaximumYPoint()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("external"));
        dxfFile.Entities.Add(new DxfLine
        {
            Layer = "external",
            P1 = new IxMilia.Dxf.DxfPoint(0, 2, 0),
            P2 = new IxMilia.Dxf.DxfPoint(3, 4, 0)
        });
        
        const double expectedY = 4.0;

        var result = dxfFile.GetMaximumExternalPoint();

        result.Y.Should().Be(expectedY);
    }
    
    [Fact(DisplayName = "When DxfFile contains polyline entities in the external layer, then return the maximum X point")]
    public void WhenDxfFileContainsPolylineEntitiesInExternalLayer_ThenReturnMaximumXPoint()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("external"));
        dxfFile.Entities.Add(new DxfPolyline(new List<DxfVertex>()
        {
            new (new IxMilia.Dxf.DxfPoint(0, 0, 0)),
            new (new IxMilia.Dxf.DxfPoint(0, 1, 0)),
            new (new IxMilia.Dxf.DxfPoint(5, 2, 0)),
            new (new IxMilia.Dxf.DxfPoint(10, 1, 0)),
            new (new IxMilia.Dxf.DxfPoint(10, 0, 0))
        })
        {
            Layer = "external"
        });
        
        const double expectedX = 10.0;

        var result = dxfFile.GetMaximumExternalPoint();

        result.X.Should().Be(expectedX);
    }
    
    [Fact(DisplayName = "When DxfFile contains polyline entities in the external layer, then return the maximum Y point")]
    public void WhenDxfFileContainsPolylineEntitiesInExternalLayer_ThenReturnMaximumYPoint()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("external"));
        dxfFile.Entities.Add(new DxfPolyline(new List<DxfVertex>()
        {
            new (new IxMilia.Dxf.DxfPoint(0, 0, 0)),
            new (new IxMilia.Dxf.DxfPoint(0, 1, 0)),
            new (new IxMilia.Dxf.DxfPoint(5, 2, 0)),
            new (new IxMilia.Dxf.DxfPoint(10, 1, 0)),
            new (new IxMilia.Dxf.DxfPoint(10, 0, 0))
        })
        {
            Layer = "external"
        });
        
        const double expectedY = 2.0;

        var result = dxfFile.GetMaximumExternalPoint();

        result.Y.Should().Be(expectedY);
    }

    [Fact(DisplayName = "When DxfFile contains no entities in the external layer, then throw exception")]
    public void WhenDxfFileContainsNoEntitiesInExternalLayer_ThenThrowException()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("external"));
        dxfFile.Entities.Add(new DxfLine
        {
            Layer = "otherLayer",
            P1 = new IxMilia.Dxf.DxfPoint(1, 2, 0),
            P2 = new IxMilia.Dxf.DxfPoint(3, 4, 0)
        });

        Assert.Throws<InvalidOperationException>(() => dxfFile.GetMaximumExternalPoint());
    }

    [Fact(DisplayName = "When DxfFile contains no entities, then throw exception")]
    public void WhenDxfFileContainsNoEntities_ThenThrowException()
    {
        var dxfFile = new DxfFile();

        Assert.Throws<InvalidOperationException>(() => dxfFile.GetMaximumExternalPoint());
    }
}

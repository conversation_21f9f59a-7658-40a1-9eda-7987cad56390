using IxMilia.Dxf;
using IxMilia.Dxf.Entities;

namespace Dxf.Core.Tests.DxfFileExtensions;

[Trait("DxfFileExtensions", "GetLengthInMeters")]
public class GetLengthInMetersTests
{
    [Fact(DisplayName =
        "When DxfFile contains upstream and downstream points in line, then return correct length in meters")]
    public void
        WhenDxfFileContainsUpstreamAndDownstreamPointsInLine_ThenReturnCorrectLengthInMeters()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("external"));
        dxfFile.Entities.Add(new DxfLine
        {
            Layer = "external",
            P1 = new IxMilia.Dxf.DxfPoint(0, 0, 0),
            P2 = new IxMilia.Dxf.DxfPoint(10, 0, 0)
        });

        const double expectedLength = 10.0;

        var result = dxfFile.GetLengthInMeters();

        result.Should().Be(expectedLength);
    }
    
    [Fact(DisplayName =
        "When DxfFile contains upstream and downstream points in polyline, then return correct length in meters")]
    public void
        WhenDxfFileContainsUpstreamAndDownstreamPointsInPolyline_ThenReturnCorrectLengthInMeters()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("external"));
        dxfFile.Entities.Add(new DxfPolyline(new List<DxfVertex>()
        {
            new (new IxMilia.Dxf.DxfPoint(0, 0, 0)),
            new (new IxMilia.Dxf.DxfPoint(0, 1, 0)),
            new (new IxMilia.Dxf.DxfPoint(5, 2, 0)),
            new (new IxMilia.Dxf.DxfPoint(10, 1, 0)),
            new (new IxMilia.Dxf.DxfPoint(10, 0, 0))
        })
        {
            Layer = "external"
        });

        const double expectedLength = 10.0;

        var result = dxfFile.GetLengthInMeters();

        result.Should().Be(expectedLength);
    }
    
    [Fact(DisplayName =
        "When DxfFile contains multiple layers, then return correct length in meters")]
    public void
        WhenDxfFileContainsMultipleLayers_ThenReturnCorrectLengthInMeters()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("layer1"));
        dxfFile.Entities.Add(new DxfLine(
            new IxMilia.Dxf.DxfPoint(0,0,0),  
            new IxMilia.Dxf.DxfPoint(15, 0, 0))
        {
            Layer = "layer1"
        });
        
        dxfFile.Layers.Add(new DxfLayer("external"));
        dxfFile.Entities.Add(new DxfPolyline(new List<DxfVertex>()
        {
            new (new IxMilia.Dxf.DxfPoint(0, 0, 0)),
            new (new IxMilia.Dxf.DxfPoint(0, 1, 0)),
            new (new IxMilia.Dxf.DxfPoint(5, 2, 0)),
            new (new IxMilia.Dxf.DxfPoint(10, 1, 0)),
            new (new IxMilia.Dxf.DxfPoint(10, 0, 0))
        })
        {
            Layer = "external"
        });

        const double expectedLength = 10.0;

        var result = dxfFile.GetLengthInMeters();

        result.Should().Be(expectedLength);
    }

    [Fact(DisplayName =
        "When DxfFile contains no entities, then throw exception")]
    public void WhenDxfFileContainsNoEntities_ThenThrowException()
    {
        var dxfFile = new DxfFile();

        Assert.Throws<InvalidOperationException>(() =>
            dxfFile.GetLengthInMeters());
    }
}
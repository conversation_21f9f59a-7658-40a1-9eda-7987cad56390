using IxMilia.Dxf;
using IxMilia.Dxf.Entities;

namespace Dxf.Core.Tests.DxfFileExtensions;

[Trait("DxfFileExtensions", "HasEntities")]
public class HasEntitiesTests
{
    [Fact(DisplayName = "When DxfFile contains entities, then return true")]
    public void WhenDxfFileContainsEntities_ThenReturnTrue()
    {
        var dxfFile = new DxfFile();
        dxfFile.Entities.Add(new DxfLine());

        var result = dxfFile.HasEntities();

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When DxfFile does not contain entities, then return false")]
    public void WhenDxfFileDoesNotContainEntities_ThenReturnFalse()
    {
        var dxfFile = new DxfFile();

        var result = dxfFile.HasEntities();

        result.Should().BeFalse();
    }
}
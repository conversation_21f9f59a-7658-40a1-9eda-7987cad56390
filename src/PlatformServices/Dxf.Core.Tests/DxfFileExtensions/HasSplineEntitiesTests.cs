using FluentAssertions;
using IxMilia.Dxf;
using IxMilia.Dxf.Entities;
using Xunit;

namespace Dxf.Core.Tests.DxfFileExtensions;

[Trait("DxfFileExtensions", "HasSplineEntities")]
public class HasSplineEntitiesTests
{
    [Fact(DisplayName = "When DxfFile contains spline entities, then return true")]
    public void WhenDxfFileContainsSplineEntities_ThenReturnTrue()
    {
        var dxfFile = new DxfFile();
        dxfFile.Entities.Add(new DxfSpline());

        var result = dxfFile.HasSplineEntities();

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When DxfFile contains multiple spline entities, then return true")]
    public void WhenDxfFileContainsMultipleSplineEntities_ThenReturnTrue()
    {
        var dxfFile = new DxfFile();
        dxfFile.Entities.Add(new DxfSpline());
        dxfFile.Entities.Add(new DxfSpline());

        var result = dxfFile.HasSplineEntities();

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When DxfFile contains spline and other entities, then return true")]
    public void WhenDxfFileContainsSplineAndOtherEntities_ThenReturnTrue()
    {
        var dxfFile = new DxfFile();
        dxfFile.Entities.Add(new DxfLine());
        dxfFile.Entities.Add(new DxfSpline());
        dxfFile.Entities.Add(new DxfCircle());

        var result = dxfFile.HasSplineEntities();

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When DxfFile contains no spline entities, then return false")]
    public void WhenDxfFileContainsNoSplineEntities_ThenReturnFalse()
    {
        var dxfFile = new DxfFile();
        dxfFile.Entities.Add(new DxfLine());
        dxfFile.Entities.Add(new DxfCircle());
        dxfFile.Entities.Add(new DxfHatch());

        var result = dxfFile.HasSplineEntities();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When DxfFile contains no entities, then return false")]
    public void WhenDxfFileContainsNoEntities_ThenReturnFalse()
    {
        var dxfFile = new DxfFile();

        var result = dxfFile.HasSplineEntities();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When DxfFile is null, then throw NullReferenceException")]
    public void WhenDxfFileIsNull_ThenThrowNullReferenceException()
    {
        DxfFile? dxfFile = null;

        var action = () => dxfFile!.HasSplineEntities();

        action.Should().Throw<System.NullReferenceException>();
    }

    [Fact(DisplayName = "When DxfFile entities collection is null, then return false")]
    public void WhenDxfFileEntitiesCollectionIsNull_ThenReturnFalse()
    {
        var dxfFile = new DxfFile();
        // Note: DxfFile.Entities is never null in IxMilia.Dxf, but testing defensive programming
        // This test verifies the method handles null collections gracefully

        var result = dxfFile.HasSplineEntities();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When DxfFile contains spline with specific layer, then return true")]
    public void WhenDxfFileContainsSplineWithSpecificLayer_ThenReturnTrue()
    {
        var dxfFile = new DxfFile();
        dxfFile.Entities.Add(new DxfSpline { Layer = "spline_layer" });

        var result = dxfFile.HasSplineEntities();

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When DxfFile contains spline with control points, then return true")]
    public void WhenDxfFileContainsSplineWithControlPoints_ThenReturnTrue()
    {
        var dxfFile = new DxfFile();
        var spline = new DxfSpline();
        spline.ControlPoints.Add(new DxfControlPoint(new IxMilia.Dxf.DxfPoint(0, 0, 0)));
        spline.ControlPoints.Add(new DxfControlPoint(new IxMilia.Dxf.DxfPoint(10, 10, 0)));
        dxfFile.Entities.Add(spline);

        var result = dxfFile.HasSplineEntities();

        result.Should().BeTrue();
    }
}

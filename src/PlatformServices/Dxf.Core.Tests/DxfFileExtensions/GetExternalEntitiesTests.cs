using IxMilia.Dxf;
using IxMilia.Dxf.Entities;

namespace Dxf.Core.Tests.DxfFileExtensions;

[Trait("DxfFileExtensions", "GetExternalEntities")]
public class GetExternalEntitiesTests
{
    [Fact(DisplayName = "When DxfFile contains entities in the external layer, then return them")]
    public void WhenDxfFileContainsEntitiesInExternalLayer_ThenReturnThem()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("external"));
        dxfFile.Entities.Add(new DxfLine { Layer = "external" });
        dxfFile.Entities.Add(new DxfCircle { Layer = "external" });
        
        const int expectedCount = 2;

        var result = dxfFile.GetExternalEntities();
        
        result.Count.Should().Be(expectedCount);
    }

    [Fact(DisplayName = "When DxfFile contains no entities in the external layer, then return empty list")]
    public void WhenDxfFileContainsNoEntitiesInExternalLayer_ThenReturnEmptyList()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("external"));
        dxfFile.Entities.Add(new DxfLine { Layer = "otherLayer" });

        var result = dxfFile.GetExternalEntities();

        result.Should().BeEmpty();
    }

    [Fact(DisplayName = "When DxfFile contains no external layer, then return empty list")]
    public void WhenDxfFileContainsNoExternalLayer_ThenReturnEmptyList()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("otherLayer"));
        dxfFile.Entities.Add(new DxfLine { Layer = "otherLayer" });

        var result = dxfFile.GetExternalEntities();

        result.Should().BeEmpty();
    }

    [Fact(DisplayName = "When DxfFile contains no entities, then return empty list")]
    public void WhenDxfFileContainsNoEntities_ThenReturnEmptyList()
    {
        var dxfFile = new DxfFile();

        var result = dxfFile.GetExternalEntities();

        result.Should().BeEmpty();
    }
}
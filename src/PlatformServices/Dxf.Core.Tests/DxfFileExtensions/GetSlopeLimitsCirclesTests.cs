using IxMilia.Dxf;
using IxMilia.Dxf.Entities;

namespace Dxf.Core.Tests.DxfFileExtensions;

[Trait("DxfFileExtensions", "GetSlopeLimitsCircles")]
public class GetSlopeLimitsCirclesTests
{
    [Fact(DisplayName = "When DxfFile contains circles in slope limits layer, then return them")]
    public void WhenDxfFileContainsCirclesInSlopeLimitsLayer_ThenReturnThem()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("slope_limits"));
        dxfFile.Entities.Add(new DxfCircle { Layer = "slope_limits" });
        dxfFile.Entities.Add(new DxfCircle { Layer = "slope_limits" });
        
        const int expectedCount = 2;

        var result = dxfFile.GetSlopeLimitsCircles();

        result.Count.Should().Be(expectedCount);
    }

    [Fact(DisplayName = "When DxfFile contains no circles in slope limits layer, then return empty list")]
    public void WhenDxfFileContainsNoCirclesInSlopeLimitsLayer_ThenReturnEmptyList()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("slope_limits"));
        dxfFile.Entities.Add(new DxfLine { Layer = "slope_limits" });

        var result = dxfFile.GetSlopeLimitsCircles();

        result.Should().BeEmpty();
    }

    [Fact(DisplayName = "When DxfFile contains circles in other layers, then return empty list")]
    public void WhenDxfFileContainsCirclesInOtherLayers_ThenReturnEmptyList()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("otherLayer"));
        dxfFile.Entities.Add(new DxfCircle { Layer = "otherLayer" });

        var result = dxfFile.GetSlopeLimitsCircles();

        result.Should().BeEmpty();
    }

    [Fact(DisplayName = "When DxfFile contains no entities, then return empty list")]
    public void WhenDxfFileContainsNoEntities_ThenReturnEmptyList()
    {
        var dxfFile = new DxfFile();

        var result = dxfFile.GetSlopeLimitsCircles();

        result.Should().BeEmpty();
    }
}
using IxMilia.Dxf;
using IxMilia.Dxf.Entities;

namespace Dxf.Core.Tests.DxfFileExtensions;

[Trait("DxfFileExtensions", "HasInsertEntitiesWithSolid")]
public class HasInsertEntitiesWithSolidTests
{
    [Fact(DisplayName = "When DxfFile does not contain any insert entities, then return false")]
    public void WhenDxfFileDoesNotContainInsertEntities_ThenReturnFalse()
    {
        var dxfFile = new DxfFile();
        dxfFile.Entities.Add(new DxfLine());

        var result = dxfFile.HasInsertEntitiesWithSolid();

        result.Should().BeFalse();
    }
}
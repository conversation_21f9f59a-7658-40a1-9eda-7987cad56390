using IxMilia.Dxf;
using IxMilia.Dxf.Entities;

namespace Dxf.Core.Tests.DxfFileExtensions;

[Trait("DxfFileExtensions", "HasWaterLine")]
public class HasWaterLineTests
{
    [Fact(DisplayName = "When DxfFile contains waterline layer and entity, then return true")]
    public void WhenDxfFileContainsWaterlineLayerAndEntity_ThenReturnTrue()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("waterline"));
        dxfFile.Entities.Add(new DxfLine { Layer = "waterline" });

        var result = dxfFile.HasWaterLine();

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When DxfFile contains waterline entity but no layer, then return false")]
    public void WhenDxfFileContainsWaterlineEntityButNoLayer_ThenReturnFalse()
    {
        var dxfFile = new DxfFile();
        dxfFile.Entities.Add(new DxfLine { Layer = "waterline" });

        var result = dxfFile.HasWaterLine();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When DxfFile contains waterline layer but no entity, then return false")]
    public void WhenDxfFileContainsWaterlineLayerButNoEntity_ThenReturnFalse()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("waterline"));

        var result = dxfFile.HasWaterLine();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When DxfFile does not contain waterline layer or entity, then return false")]
    public void WhenDxfFileDoesNotContainWaterlineLayerOrEntity_ThenReturnFalse()
    {
        var dxfFile = new DxfFile();
        dxfFile.Layers.Add(new DxfLayer("otherLayer"));
        dxfFile.Entities.Add(new DxfLine { Layer = "otherLayer" });

        var result = dxfFile.HasWaterLine();

        result.Should().BeFalse();
    }
}
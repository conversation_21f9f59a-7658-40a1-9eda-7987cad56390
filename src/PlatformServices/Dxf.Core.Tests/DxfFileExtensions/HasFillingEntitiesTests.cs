using IxMilia.Dxf;
using IxMilia.Dxf.Entities;

namespace Dxf.Core.Tests.DxfFileExtensions;

[Trait("DxfFileExtensions", "HasFillingEntities")]
public class HasFillingEntitiesTests
{
    [Fact(DisplayName = "When DxfFile contains hatch or solid entities, then return true")]
    public void WhenDxfFileContainsHatchOrSolidEntities_ThenReturnTrue()
    {
        var dxfFile = new DxfFile();
        dxfFile.Entities.Add(new DxfHatch { Layer = "filling" });
        dxfFile.Entities.Add(new DxfSolid { Layer = "filling" });

        var result = dxfFile.HasFillingEntities();

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When DxfFile contains no hatch or solid entities, then return false")]
    public void WhenDxfFileContainsNoHatchOrSolidEntities_ThenReturnFalse()
    {
        var dxfFile = new DxfFile();
        dxfFile.Entities.Add(new DxfLine { Layer = "otherLayer" });

        var result = dxfFile.HasFillingEntities();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When DxfFile contains no entities, then return false")]
    public void WhenDxfFileContainsNoEntities_ThenReturnFalse()
    {
        var dxfFile = new DxfFile();

        var result = dxfFile.HasFillingEntities();

        result.Should().BeFalse();
    }
}
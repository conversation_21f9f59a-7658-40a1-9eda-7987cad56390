using System;
using System.Text.Json;
using Domain.Messages.Commands.Simulation;

namespace Database.Repositories.Simulation.Extensions;

public static class SimulationExtensions
{
    public static object ToDbWriteParam(
        this Domain.Entities.Simulation simulation)
    {
        return new
        {
            simulation.Id,
            simulation.Name,
            simulation.Status,
            simulation.ShouldEvaluateDrainedCondition,
            simulation.ShouldEvaluateUndrainedCondition,
            simulation.ShouldEvaluatePseudoStaticCondition,
            simulation.SafetyFactorTarget,
            SeismicCoefficientHorizontal =
                simulation.SeismicCoefficient?.Horizontal,
            SeismicCoefficientVertical =
                simulation.SeismicCoefficient?.Vertical,
            simulation.Slide2Configuration.CircularParameters
                ?.CircularSearchMethod,
            CircularDivisionsAlongSlope = simulation.Slide2Configuration
                .CircularParameters
                ?.DivisionsAlongSlope,
            simulation.Slide2Configuration.CircularParameters
                ?.CirclesPerDivision,
            CircularNumberOfIterations = simulation.Slide2Configuration
                .CircularParameters
                ?.NumberOfIterations,
            CircularDivisionsNextIteration = simulation.Slide2Configuration
                .CircularParameters
                ?.DivisionsNextIteration,
            simulation.Slide2Configuration.CircularParameters?.RadiusIncrement,
            CircularNumberOfSurfaces = simulation.Slide2Configuration
                .CircularParameters
                ?.NumberOfSurfaces,
            simulation.Slide2Configuration.NonCircularParameters
                ?.NonCircularSearchMethod,
            NonCircularDivisionsAlongSlope = simulation.Slide2Configuration
                .NonCircularParameters
                ?.DivisionsAlongSlope,
            simulation.Slide2Configuration.NonCircularParameters
                ?.SurfacesPerDivision,
            NonCircularNumberOfIterations = simulation.Slide2Configuration
                .NonCircularParameters
                ?.NumberOfIterations,
            NonCircularDivisionsNextIteration = simulation.Slide2Configuration
                .NonCircularParameters
                ?.DivisionsNextIteration,
            simulation.Slide2Configuration.NonCircularParameters
                ?.NumberOfVerticesAlongSurface,
            NonCircularNumberOfSurfaces = simulation.Slide2Configuration
                .NonCircularParameters
                ?.NumberOfSurfaces,
            simulation.Slide2Configuration.NonCircularParameters?.NumberOfNests,
            simulation.Slide2Configuration.NonCircularParameters
                ?.MaximumIterations,
            simulation.Slide2Configuration.NonCircularParameters
                ?.InitialNumberOfSurfaceVertices,
            simulation.Slide2Configuration.NonCircularParameters
                ?.InitialNumberOfIterations,
            simulation.Slide2Configuration.NonCircularParameters
                ?.MaximumNumberOfSteps,
            NumberOfFactorsSafetyCompared = simulation.Slide2Configuration
                .NonCircularParameters
                ?.NumberOfFactorsSafetyComparedBeforeStopping,
            simulation.Slide2Configuration.NonCircularParameters
                ?.ToleranceForStoppingCriterion,
            simulation.Slide2Configuration.NonCircularParameters
                ?.NumberOfParticles,
            simulation.WaterTableConfiguration,
            simulation.ReadingStatisticalMeasure,
            simulation.WaterTableVariation,
            simulation.StartDate,
            simulation.EndDate,
            simulation.UpstreamLinimetricRulerStatisticalMeasure,
            simulation.UpstreamLinimetricRulerQuota,
            simulation.DownstreamLinimetricRulerStatisticalMeasure,
            simulation.DownstreamLinimetricRulerQuota,
            simulation.IgnoreDamagedInstruments,
            simulation.NeedToDoStatisticalCalculations,
            CreatedBy = simulation.CreatedBy.Id,
            UpstreamLinimetricRulerId = simulation.UpstreamLinimetricRuler?.Id,
            DownstreamLinimetricRulerId = simulation.DownstreamLinimetricRuler
                ?.Id
        };
    }

    public static object ToDbUpdateStatusParam(
        this Domain.Entities.Simulation simulation)
    {
        return new { simulation.Id, simulation.Status };
    }

    public static object ToOutboxParam(
        this Domain.Entities.Simulation simulation)
    {
        return new Domain.Entities.Outbox
        {
            Id = Guid.NewGuid(),
            Type = typeof(StartSimulation).AssemblyQualifiedName,
            Data = JsonSerializer.Serialize(
                new StartSimulation { Id = simulation.Id })
        };
    }
}
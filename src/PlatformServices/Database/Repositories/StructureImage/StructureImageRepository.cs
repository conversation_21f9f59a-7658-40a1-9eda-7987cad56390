using Dapper;
using Domain.ValueObjects;
using Model.Image._Shared;
using Model.Image.Delete.Request;
using Model.Image.Search.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Database.Repositories.StructureImage
{
    public class StructureImageRepository : IStructureImageRepository
    {
        private readonly string _connectionString;

        public StructureImageRepository(string connectionString)
        {
            _connectionString = connectionString;
        }

        public async Task AddAsync(Domain.Entities.StructureImage item)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();

            var param = new
            {
                item.Id,
                ImageFileName = item.ImageFile.Name,
                ImageFileUniqueName = item.ImageFile?.UniqueName,
                ThumbnailFileName = item.ThumbnailFile.Name,
                ThumbnailFileUniqueName = item.ThumbnailFile?.UniqueName,
                item.StructureId,
                item.SizeInKB,
                item.Description,
                item.CreatedById
            };

            await connection.ExecuteAsync(Queries.Insert, param);
        }

        public async Task<int> CountByStructureAsync(Guid structureId)
        {
            var param = new { structureId };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.ExecuteScalarAsync<int>(Queries.CountByStructureAsync, param);
        }

        public async Task<Domain.Entities.StructureImage> GetAsync(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var client = await connection.QueryAsync(
                sql: Queries.GetById,
                new[]
                {
                    typeof(Domain.Entities.StructureImage),
                    typeof(File),
                    typeof(File)
                },
                (records) =>
                {
                    var image = records[0] as Domain.Entities.StructureImage;
                    var imageFile = records[1] as File;
                    var thumbnailFile = records[2] as File;

                    image.ImageFile = imageFile;
                    image.ThumbnailFile = thumbnailFile;

                    return image;
                },
                splitOn: "Name,Name",
                param: new
                {
                    Id = id
                });

            return client.FirstOrDefault();
        }

        public async Task<List<SearchResult>> SearchImagesAsync(SearchImagesRequest request)
        {
            var sqlBuilder = new StringBuilder();
            sqlBuilder.Append(Queries.SearchImages); // Start with the base query

            // Dynamic parameters object to add filter values
            var parameters = new DynamicParameters();

            // Track if a WHERE clause has been added
            bool whereAdded = false;

            // Helper function to append conditions
            Action<string, string, object> addCondition = (condition, paramName, value) =>
            {
                if (!whereAdded)
                {
                    sqlBuilder.Append(" WHERE ");
                    whereAdded = true;
                }
                else
                {
                    sqlBuilder.Append(" AND ");
                }
                sqlBuilder.Append(condition);
                parameters.Add(paramName, value);
            };

            // Determine the deepest level filter provided and build the WHERE clause accordingly
            if (request.Query.Filters.Structures.Any())
            {
                var structures = request.Query.Filters.Structures;
                addCondition(Queries.ConditionalStructuresWhereClause, "@Structures", structures);
            }
            else if (request.Query.Filters.ClientUnits.Any())
            {
                var clientUnits = request.Query.Filters.ClientUnits;
                addCondition(Queries.SearchImages_ConditionalClientUnitsWhereClause, "@ClientUnits", clientUnits);
            }
            else if (request.Query.Filters.Clients.Any())
            {
                var clients = request.Query.Filters.Clients;
                addCondition(Queries.SearchImages_ConditionalClientsWhereClause, "@Clients", clients);
            }

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            var query = sqlBuilder.ToString();

            return (await connection.QueryAsync<SearchResult>(
                sql: query,
                new[]
                {
                    typeof(Domain.Entities.StructureImage),
                    typeof(string)
                },
                (records) =>
                {
                    var image = records[0] as Domain.Entities.StructureImage;
                    var uploaderUserName = records[1] as string;

                    return new()
                    {
                        Id = image.Id,
                        Type = "Structure",
                        Description = image.Description,
                        UploadedDate = image.CreatedDate,
                        Base64ImageUrl = $"/api/v1/images/{image.Id}/base64",
                        Base64ThumbnailUrl = $"/api/v1/images/{image.Id}/base64?thumbnail=true",
                        UploadedBy = uploaderUserName.Trim()
                    };
                },
                splitOn: "Username",
                param: parameters
                )).ToList();
        }

        public async Task<int> DeleteAsync(DeleteImageRequest request)
        {
            var sqlBuilder = new StringBuilder();
            sqlBuilder.Append(Queries.Delete);

            // Dynamic parameters object to add filter values
            var parameters = new DynamicParameters();

            parameters.Add("@Id", request.ImageId);

            // Track if a WHERE clause has been added
            bool whereAdded = false;

            // Helper function to append conditions
            Action<string, string, object> addCondition = (condition, paramName, value) =>
            {
                if (!whereAdded)
                {
                    sqlBuilder.Append(" WHERE ");
                    whereAdded = true;
                }
                else
                {
                    sqlBuilder.Append(" AND ");
                }
                sqlBuilder.Append(condition);
                parameters.Add(paramName, value);
            };

            // If the requesting user is not of SuperSupport role, limit to the structures they have access to
            if (!request.RequestedBySuperSupport)
                addCondition(Queries.ConditionalStructuresWhereClause, "@Structures", request.RequestedUserStructures);

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            var query = sqlBuilder.ToString();

            // Should be one or zero, as the main argument is the primary key (id).
            // It could be zero, in case the user did not have permission to delete the image or if the id is invalid
            var affectedRows = await connection.ExecuteAsync(sql: query, param: parameters);

            return affectedRows;
        }

        public async Task UpdateAsync(Domain.Entities.StructureImage item)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();

            var param = new { item.Id, item.Description };

            await connection
                 .ExecuteAsync(Queries.Update, param);
        }

        public Task DeleteAsync(Guid id)
        {
            throw new NotImplementedException();
        }
    }
}

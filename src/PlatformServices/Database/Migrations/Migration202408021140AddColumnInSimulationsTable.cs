using FluentMigrator;

namespace Database.Migrations
{
    [Migration(202408021140)]
    public class Migration202408021140AddColumnInSimulationsTable : Migration
    {
        public override void Up()
        {
            Alter.Table("simulations")
                .AddColumn("should-keep")
                .AsBoolean()
                .NotNullable()
                .WithDefaultValue(false);

            Create.Index("idx-simulations-should-keep")
                .OnTable("simulations")
                .OnColumn("should-keep")
                .Ascending();
        }

        public override void Down()
        {
            Delete.Index("idx-simulations-should-keep")
                .OnTable("simulations");

            Delete.Column("should-keep")
                .FromTable("simulations");
        }
    }
}

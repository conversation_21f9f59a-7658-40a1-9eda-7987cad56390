using Domain.Entities;
using Dxf.Core.Strategy;
using Dxf.Core.Strategy.Factory;
using Geometry.Core;
using IxMilia.Dxf;
using IxMilia.Dxf.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using Dxf.Core.Extensions;
using Section = Domain.Entities.Section;

namespace Dxf.Core;

public static class DxfFileExtensions
{
    public static bool HasWaterLine(this DxfFile dxfFile) =>
        dxfFile.Layers.Any(layer => layer.Name.ContainsWaterKeyword())
        && dxfFile.Entities.Any(entity => entity.Layer.ContainsWaterKeyword());

    public static bool HasLimitsDefined(this DxfFile dxfFile) =>
        dxfFile.Layers.Any(layer => layer.Name.ContainsLimitsKeyword())
        && dxfFile.Entities.Any(entity => entity.Layer.ContainsLimitsKeyword());
    
    public static bool HasEntities(this DxfFile dxfFile) =>
        dxfFile.Entities?.Any() ?? false;

    public static bool HasLayers(this DxfFile dxfFile) =>
        dxfFile.Layers?.Any() ?? false;
    
    public static bool HasFillingEntities(this DxfFile dxfFile) =>
        dxfFile.Entities
            .Any(entity =>
                entity.EntityType is DxfEntityType.Hatch
                    or DxfEntityType.Solid);

    public static bool HasInsertEntitiesWithSolid(this DxfFile dxfFile) =>
        dxfFile.Entities
            .Where(entity => entity.EntityType == DxfEntityType.Insert)
            .Select(entity => (DxfInsert)entity)
            .Any(insert =>
                insert.Entities.Any(y => y.EntityType == DxfEntityType.Solid));
    
    public static bool HasExternalLayer(this DxfFile dxfFile) =>
        dxfFile.Layers.Any(layer => layer.Name.ToLower() == "external");

    public static List<DxfCircle> GetSlopeLimitsCircles(this DxfFile dxfFile) =>
        dxfFile.Entities
            .Where(entity => entity.Layer.ContainsLimitsKeyword() &&
                             entity.EntityType == DxfEntityType.Circle)
            .Select(entity => (DxfCircle)entity)
            .ToList();

    public static List<DxfEntity> GetExternalEntities(this DxfFile dxfFile) =>
        dxfFile.Entities
            .Where(x => x.Layer.ToLower() == "external")
            .ToList();
    
    public static IxMilia.Dxf.DxfPoint GetMinimumExternalPoint(this DxfFile dxfFile)
    {
        var externalEntities = dxfFile
            .GetExternalEntities();

        if (!externalEntities.Any())
        {
            throw new InvalidOperationException("No external entities found");
        }
        
        return externalEntities
            .Min(entity => entity.GetBoundingBox()!.Value.MinimumPoint);
    }

    public static IxMilia.Dxf.DxfPoint GetMaximumExternalPoint(this DxfFile dxfFile)
    {
        var externalEntities = dxfFile
            .GetExternalEntities();

        if (!externalEntities.Any())
        {
            throw new InvalidOperationException("No external entities found");
        }

        return externalEntities
            .Max(entity => entity.GetBoundingBox()!.Value.MaximumPoint);
    }

    public static bool SlopeLimitsAreOutsideExternalBoundary(
        this DxfFile dxfFile)
    {
        var circles = dxfFile.GetSlopeLimitsCircles();
        if (!circles.Any())
        {
            return false;
        }

        var minX = dxfFile.GetMinimumExternalPoint().X;
        var maxX = dxfFile.GetMaximumExternalPoint().X;

        return circles
            .Any(x => x.Center.X < minX || x.Center.X > maxX);
    }

    public static double GetLengthInMeters(this DxfFile dxfFile)
    {
        var minimumExternalPoint = dxfFile.GetMinimumExternalPoint();
        var maximumExternalPoint = dxfFile.GetMaximumExternalPoint();

        return Helper.GetDistance(
            new PointD(minimumExternalPoint.X, 0),
            new PointD(maximumExternalPoint.X, 0));
    }

    public static List<int> GetMaterialsSearchIdentifiers(this DxfFile dxfFile)
    {
        var result = new List<int>();

        result.AddRange(dxfFile
            .Entities
            .Where(entity =>
                entity.EntityType is DxfEntityType.Hatch or DxfEntityType.Solid)
            .Select(entity => entity.Layer)
            .Where(layer => layer != "0" && int.TryParse(layer, out _))
            .Select(layer => int.Parse(layer))
            .Distinct()
            .ToList());

        result.AddRange(dxfFile
            .Entities
            .Where(entity => entity.EntityType == DxfEntityType.Insert)
            .Select(entity => (DxfInsert)entity)
            .SelectMany(insert => insert.Entities)
            .Where(entity => entity.EntityType == DxfEntityType.Solid)
            .Select(entity => entity.Layer)
            .Where(layer => layer != "0" && int.TryParse(layer, out _))
            .Select(layer => int.Parse(layer))
            .Distinct()
            .ToList());

        return result;
    }
    
    public static PointD GetFirstPointOfSectionUpstream(this DxfFile dxfFile)
    {
        var pointDList = new List<PointD>();

        pointDList.AddRange(dxfFile.Entities
            .Where(x => x.EntityType == DxfEntityType.Polyline)
            .Select(x => (DxfPolyline)x)
            .SelectMany(x => x.Vertices)
            .Select(x => new PointD(x.Location.X, x.Location.Y))
            .ToList());

        pointDList.AddRange(dxfFile.Entities
            .Where(x => x.EntityType == DxfEntityType.LwPolyline)
            .Select(x => (DxfLwPolyline)x)
            .SelectMany(x => x.Vertices)
            .Select(x => new PointD(x.X, x.Y)));

        pointDList.AddRange(dxfFile.Entities
            .Where(x => x.EntityType == DxfEntityType.Line)
            .SelectMany(x => new[]
            {
                new PointD(((DxfLine)x).P1.X, ((DxfLine)x).P1.Y),
                new PointD(((DxfLine)x).P2.X, ((DxfLine)x).P2.Y)
            })
            .ToList());

        return pointDList.OrderBy(p => p.X)
            .ThenBy(p => p.Y)
            .First();
    }

    public static PointD GetFirstPointOfSectionDownstream(this DxfFile dxfFile)
    {
        var polylines = dxfFile.Entities
            .Where(x => x.EntityType == DxfEntityType.Polyline)
            .Select(x => (DxfPolyline)x);

        if (polylines.Any())
        {
            var polylinesVertices = polylines.SelectMany(x => x.Vertices);

            var downstreamPolyline = polylinesVertices
                .OrderByDescending(p => p.Location.X)
                .ThenBy(p => p.Location.Y)
                .First();

            return new(downstreamPolyline.Location.X,
                downstreamPolyline.Location.Y);
        }

        var lwPolylines = dxfFile.Entities
            .Where(x => x.EntityType == DxfEntityType.LwPolyline)
            .Select(x => (DxfLwPolyline)x);

        if (lwPolylines.Any())
        {
            var lwPolylinesVertices = lwPolylines.SelectMany(x => x.Vertices);

            var downstreamLwPolyline = lwPolylinesVertices
                .OrderByDescending(p => p.X)
                .ThenBy(p => p.Y)
                .First();

            return new(downstreamLwPolyline.X, downstreamLwPolyline.Y);
        }

        throw new Exception(
            "Could not find the polyline representing downstream.");
    }

    public static DxfPoint AddInstrument(
        this DxfFile dxfFile,
        Instrument instrument,
        Section section,
        DxfColor instrumentColor = null)
    {
        var instrumentCoordinate = instrument.CoordinateSetting.Systems.Utm;

        if (instrument.CoordinateSetting.Datum != section.Coordinates.Datum)
        {
            instrument.CoordinateSetting.Systems.Utm =
                Coordinate.Core.Helper.ToDatum(
                    instrument.CoordinateSetting.Datum,
                    section.Coordinates.Datum, instrumentCoordinate);
        }

        var upstreamCoordinateInFile = GetFirstPointOfSectionUpstream(dxfFile);

        double x;

        if (section.IsSkew)
        {
            x = Helper.GetInstrumentXPoint(
                upstreamCoordinateInFile,
                section.Coordinates.UpstreamCoordinateSetting.CoordinateSystems
                    .Utm.ToPointD(),
                section.Coordinates.DownstreamCoordinateSetting
                    .CoordinateSystems.Utm.ToPointD(),
                section.Coordinates.MidpointCoordinateSetting.CoordinateSystems
                    .Utm.ToPointD(),
                instrumentCoordinate.ToPointD());
        }
        else
        {
            x = Helper.GetInstrumentXPoint(
                upstreamCoordinateInFile,
                section.Coordinates.UpstreamCoordinateSetting.CoordinateSystems
                    .Utm.ToPointD(),
                section.Coordinates.DownstreamCoordinateSetting
                    .CoordinateSystems.Utm.ToPointD(),
                instrumentCoordinate.ToPointD());
        }

        var strategy = instrument.Type.GetStrategy();

        if (strategy == null)
        {
            return null;
        }

        var dxfStrategy = new DxfStrategy(strategy);

        return dxfStrategy.DrawInstrument(dxfFile, instrument, x,
            instrumentColor);
    }

    public static void AddInstrumentIdentifier(
        this DxfFile dxfFile,
        List<DxfPoint> points)
    {
        var upstream = dxfFile.GetFirstPointOfSectionUpstream();
        var downstream = dxfFile.GetFirstPointOfSectionDownstream();

        var distance = Helper.GetDistance(upstream, downstream);

        var textHeight =
            distance *
            0.01; // 1% of the distance between the first and last point
        var textHeightWithSpacing =
            textHeight +
            1; // Adding 1 to have a little space between texts that are above each other
        var textWidthFactor =
            0.35 * textHeight *
            2; // 0.35 is the default value for the text width factor

        foreach (var point in points)
        {
            if (point == null)
            {
                continue;
            }

            ModifyCoordinatesIfThereIsCollision(point, points,
                textHeightWithSpacing, textWidthFactor);

            dxfFile.Entities
                .Add(new DxfText(new(point.Point.X, point.NewY, 0), textHeight,
                    point.InstrumentIdentifier)
                {
                    Color = point.Color,
                    Layer = point.InstrumentIdentifier
                });

            // Draw a vertical line to the new point where the instrument identifier will be
            dxfFile.Entities
                .Add(new DxfLine(new(point.Point.X, point.Point.Y, 0),
                    new(point.Point.X, point.NewY, 0))
                {
                    Color = point.Color,
                    Layer = point.InstrumentIdentifier
                });

            dxfFile.Entities
                .Add(new DxfLine(new(point.Point.X, point.NewY, 0),
                    new(
                        point.Point.X + (point.InstrumentIdentifier.Length *
                                         textWidthFactor), point.NewY, 0))
                {
                    Color = point.Color,
                    Layer = point.InstrumentIdentifier
                });
        }
    }

    private static void ModifyCoordinatesIfThereIsCollision(
        DxfPoint point,
        List<DxfPoint> points,
        double textHeight,
        double textWidthFactor)
    {
        var isCollisionDetected = false;

        foreach (var p in points)
        {
            if (point == null || p == null ||
                p.InstrumentIdentifier == point.InstrumentIdentifier)
            {
                continue;
            }

            var distanceFromPointToLastPointOfText =
                point.InstrumentIdentifier.Length * textWidthFactor;
            var lastPointText =
                point.Point.X + distanceFromPointToLastPointOfText;

            var distancePointToP = Math.Abs(point.Point.X - p.Point.X);

            var pointXWillCollide =
                distancePointToP <= distanceFromPointToLastPointOfText;
            var pointYWillCollide = Math.Abs(point.NewY - p.NewY) < textHeight;

            if (pointXWillCollide && pointYWillCollide)
            {
                isCollisionDetected = true;
                point.NewY += textHeight;

                break;
            }
        }

        if (isCollisionDetected)
        {
            ModifyCoordinatesIfThereIsCollision(point, points, textHeight,
                textWidthFactor);
        }
    }

    public static void SetLayerColors(this DxfFile dxfFile)
    {
        foreach (var entity in dxfFile.Entities)
        {
            var entityLayer =
                dxfFile.Layers.FirstOrDefault(l => l.Name == entity.Layer);

            if (entityLayer == null)
            {
                continue;
            }

            if (entity.Color != DxfColor.ByLayer)
            {
                entityLayer.Color = entity.Color;
            }
        }
    }

    public static bool HasSplineEntities(this DxfFile dxfFile)
    {
        return dxfFile.Entities
            .Any(dxfEntity => dxfEntity.EntityType == DxfEntityType.Spline);
    }
}
using FluentValidation;

namespace Model.Core.Search.Pagination
{
    public class PaginationRequestValidator : AbstractValidator<PaginationRequest>
    {
        public PaginationRequestValidator()
        {
            CascadeMode = CascadeMode.Stop;

            RuleFor(x => x.Page)
                .GreaterThanOrEqualTo(1);

            RuleFor(x => x.PageSize)
                .NotEmpty()
                .LessThanOrEqualTo(30)
                .GreaterThanOrEqualTo(1);
        }
    }
}

using KellermanSoftware.CompareNetObjects;
using KellermanSoftware.CompareNetObjects.TypeComparers;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Application.Extensions.Comparer
{
    public class ConstructionStageComparer : BaseTypeComparer
    {
        public ConstructionStageComparer(RootComparer rootComparer) : base(rootComparer) { }

        public override bool IsTypeMatch(Type type1, Type type2)
        {
            return type1 == typeof(List<Domain.Entities.ConstructionStage>) && type2 == typeof(List<Domain.Entities.ConstructionStage>);
        }

        public override void CompareType(CompareParms parms)
        {
            var list1 = (List<Domain.Entities.ConstructionStage>)parms.Object1;
            var list2 = (List<Domain.Entities.ConstructionStage>)parms.Object2;

            var list1Ids = new HashSet<Guid>(list1.Select(r => r.Id));
            var list2Ids = new HashSet<Guid>(list2.Select(r => r.Id));

            var addedConstructionStages = list2.Where(r => !list1Ids.Contains(r.Id)).ToList();
            var existingConstructionStages = list2.Where(r => list1Ids.Contains(r.Id)).ToList();
            var removedConstructionStages = list1.Where(i => !list2Ids.Contains(i.Id)).ToList();

            foreach (var added in addedConstructionStages)
            {
                var difference = new Difference
                {
                    PropertyName = $"{parms.BreadCrumb}.Adicionada({added.Stage})",
                    Object1Value = string.Empty,
                    Object2Value = added.Id.ToString(),
                    ParentObject1 = parms.Object1,
                    ParentObject2 = parms.Object2
                };

                parms.Result.Differences.Add(difference);
            }

            foreach (var removed in removedConstructionStages)
            {
                var difference = new Difference
                {
                    PropertyName = $"{parms.BreadCrumb}.Removida({removed.Stage})",
                    Object1Value = string.Empty,
                    Object2Value = removed.Id.ToString(),
                    ParentObject1 = parms.Object1,
                    ParentObject2 = parms.Object2
                };

                parms.Result.Differences.Add(difference);
            }

            foreach (var existing in existingConstructionStages)
            {
                var original = list1.FirstOrDefault(r => r.Id == existing.Id);
                Compare(parms, original, existing);
            }
        }

        private static void Compare(CompareParms parms, Domain.Entities.ConstructionStage original, Domain.Entities.ConstructionStage modified)
        {
            var originalReviewProps = original.GetType().GetProperties();
            var membersToIgnore = parms.Config.MembersToIgnore;
            var maxDateDiffMilliseconds = parms.Config.MaxMillisecondsDateDifference;

            foreach (var prop in originalReviewProps)
            {
                var propName = $"{parms.BreadCrumb}.{prop.Name}";

                if (membersToIgnore.Contains(propName) || membersToIgnore.Contains(prop.Name))
                {
                    continue;
                }

                var originalValue = prop.GetValue(original);
                var modifiedValue = prop.GetValue(modified);

                if (originalValue is DateTime originalDateTime && modifiedValue is DateTime modifiedDateTime)
                {
                    if (Math.Abs((modifiedDateTime - originalDateTime).TotalMilliseconds) > maxDateDiffMilliseconds)
                    {
                        AddDifference(parms, prop.Name, originalValue, modifiedValue);
                    }

                    continue;
                }

                if (!Equals(originalValue, modifiedValue))
                {
                    if (originalValue is Domain.ValueObjects.File || modifiedValue is Domain.ValueObjects.File)
                    {
                        AddDifference(
                            parms,
                            prop.Name,
                            originalValue == null ? originalValue : ((Domain.ValueObjects.File)originalValue).Name,
                            modifiedValue == null ? modifiedValue : ((Domain.ValueObjects.File)modifiedValue).Name
                        );
                        continue;
                    }

                    AddDifference(parms, prop.Name, originalValue, modifiedValue);
                }
            }
        }

        private static void AddDifference(CompareParms parms, string propName, object originalValue, object modifiedValue)
        {
            var difference = new Difference
            {
                PropertyName = $"{parms.BreadCrumb}.{propName}",
                Object1Value = originalValue?.ToString(),
                Object2Value = modifiedValue?.ToString(),
                ParentObject1 = parms.Object1,
                ParentObject2 = parms.Object2
            };

            parms.Result.Differences.Add(difference);
        }
    }
}

using Application.Instrument.GetFile.Strategy.Behavior;
using Domain.Enums;

namespace Application.Instrument.GetFile.Strategy.Factory
{
    public static class FileFactory
    {
        public static IFileStrategy GetStrategy(
            this FileFormat fileFormat,
            InstrumentType type)
        {
            return fileFormat switch
            {
                FileFormat.Csv => GetCsvStrategy(type),
                FileFormat.Xlsx => GetXlsxStrategy(type),
                _ => null
            };
        }

        private static IFileStrategy GetXlsxStrategy(InstrumentType type)
        {
            return type switch
            {
                InstrumentType.IPIInclinometer or InstrumentType.ConventionalInclinometer => new XlsxInclinometersStrategy(),
                InstrumentType.ElectricPiezometer => new XlsxElectricPiezometerStrategy(),
                InstrumentType.OpenStandpipePiezometer => new XlsxOpenStandpipePiezometerStrategy(),
                InstrumentType.Geophone => new XlsxGeophoneStrategy(),
                InstrumentType.Prism => new XlsxPrismStrategy(),
                InstrumentType.SettlementGauge => new XlsxSettlementGaugeStrategy(),
                InstrumentType.SurfaceLandmark => new XlsxSurfaceLandmarkStrategy(),
                InstrumentType.WaterLevelIndicator => new XlsxWaterLevelIndicatorStrategy(),
                InstrumentType.LinimetricRuler => new XlsxLinimetricRulerStrategy(),
                InstrumentType.Pluviometer => new XlsxPluviometerStrategy(),
                InstrumentType.Pluviograph => new XlsxPluviographStrategy(),
                _ => null,
            };
        }

        private static IFileStrategy GetCsvStrategy(InstrumentType type)
        {
            return type switch
            {
                InstrumentType.IPIInclinometer or InstrumentType.ConventionalInclinometer => new CsvInclinometersStrategy(),
                InstrumentType.ElectricPiezometer => new CsvElectricPiezometerStrategy(),
                InstrumentType.OpenStandpipePiezometer => new CsvOpenStandpipePiezometerStrategy(),
                InstrumentType.Geophone => new CsvGeophoneStrategy(),
                InstrumentType.Prism => new CsvPrismStrategy(),
                InstrumentType.SettlementGauge => new CsvSettlementGaugeStrategy(),
                InstrumentType.SurfaceLandmark => new CsvSurfaceLandmarkStrategy(),
                InstrumentType.WaterLevelIndicator => new CsvWaterLevelIndicatorStrategy(),
                InstrumentType.LinimetricRuler => new CsvLinimetricRulerStrategy(),
                InstrumentType.Pluviometer => new CsvPluviometerStrategy(),
                InstrumentType.Pluviograph => new CsvPluviographStrategy(),
                _ => null,
            };
        }
    }
}

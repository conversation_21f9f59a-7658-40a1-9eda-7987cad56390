using Application.Extensions;
using ClosedXML.Excel;
using Coordinate.Core.Enums;
using static Application.Instrument.GetTemplateFile.TemplateHeaders;

namespace Application.Instrument.GetTemplateFile.Strategy.Behavior
{
    public class XlsxOpenStandpipePiezometerStrategy : IFileStrategy
    {
        public byte[] GetFile(Datum datum, int rows, int searchIdentifier)
        {
            var workbook = new XLWorkbook();
            var columns = new (string, string)[]
            {
                (Names.Structure, Comments.Structure),
                (Names.Identifier, Comments.Identifier),
                (Names.AlternativeName, Comments.AlternativeName),
                (Names.BaseQuota, Comments.BaseQuota),
                (Names.TopQuota, Comments.TopQuota),
                (Names.DryType, Comments.DryType),
                (Names.Datum, Comments.Datum),
                (Names.Latitude, Comments.Latitude),
                (Names.Longitude, Comments.Longitude),
                (Names.Easting, Comments.Easting),
                (Names.Northing, Comments.Northing),
                (Names.ZoneNumber, Comments.ZoneNumber),
                (Names.ZoneLetter, Comments.ZoneLetter),
                (Names.Automated, Comments.Automated),
                (Names.Online, Comments.Online),
                (Names.Model, Comments.Model),
                (Names.Responsible, Comments.Responsible),
                (Names.InstallationDate, Comments.InstallationDate),
                (Names.AttentionLevel, Comments.AttentionLevel),
                (Names.AlertLevel, Comments.AlertLevel),
                (Names.EmergencyLevel, Comments.EmergencyLevel),
                (Names.AbruptVariation, Comments.AbruptVariation)
            };

            var ws = workbook.SetColumnNamesAndComments(columns, "Piezômetro de tudo aberto");

            for (int i = 2; i <= rows + 1; i++)
            {
                ws.Column(1).Cell(i).Value = searchIdentifier;
                ws.Column(7).Cell(i).Value = datum.ToString();
            }

            return workbook.SaveAsBytes();
        }
    }
}

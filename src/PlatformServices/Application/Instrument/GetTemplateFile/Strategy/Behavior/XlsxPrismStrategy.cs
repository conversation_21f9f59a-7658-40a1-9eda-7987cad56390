using Application.Extensions;
using ClosedXML.Excel;
using Coordinate.Core.Enums;
using static Application.Instrument.GetTemplateFile.TemplateHeaders;

namespace Application.Instrument.GetTemplateFile.Strategy.Behavior
{
    public class XlsxPrismStrategy : IFileStrategy
    {
        public byte[] GetFile(Datum datum, int rows, int searchIdentifier)
        {
            var workbook = new XLWorkbook();

            var columns = new (string, string)[]
            {
                (Names.Structure, Comments.Structure),
                (Names.Identifier, Comments.Identifier),
                (Names.AlternativeName, Comments.AlternativeName),
                (Names.TopQuota, Comments.TopQuota),
                (Names.Azimuth, Comments.Azimuth),
                (Names.UpperLimit, Comments.UpperLimit),
                (Names.LowerLimit, Comments.LowerLimit),
                (Names.Datum, Comments.Datum),
                (Names.Latitude, Comments.Latitude),
                (Names.Longitude, Comments.Longitude),
                (Names.Easting, Comments.Easting),
                (Names.Northing, Comments.Northing),
                (Names.ZoneNumber, Comments.ZoneNumber),
                (Names.ZoneLetter, Comments.ZoneLetter),
                (Names.Automated, Comments.Automated),
                (Names.Online, Comments.Online),
                (Names.Model, Comments.Model),
                (Names.Responsible, Comments.Responsible),
                (Names.InstallationDate, Comments.InstallationDate),
                (Names.ADisplacementAttentionLevel, Comments.ADisplacementAttentionLevel),
                (Names.ADisplacementAlertLevel, Comments.ADisplacementAlertLevel),
                (Names.ADisplacementEmergencyLevel, Comments.ADisplacementEmergencyLevel),
                (Names.ADisplacementAbruptVariation, Comments.ADisplacementAbruptVariation),
                (Names.BDisplacementAttentionLevel, Comments.BDisplacementAttentionLevel),
                (Names.BDisplacementAlertLevel, Comments.BDisplacementAlertLevel),
                (Names.BDisplacementEmergencyLevel, Comments.BDisplacementEmergencyLevel),
                (Names.BDisplacementAbruptVariation, Comments.BDisplacementAbruptVariation),
                (Names.NDisplacementAttentionLevel, Comments.NDisplacementAttentionLevel),
                (Names.NDisplacementAlertLevel, Comments.NDisplacementAlertLevel),
                (Names.NDisplacementEmergencyLevel, Comments.NDisplacementEmergencyLevel),
                (Names.NDisplacementAbruptVariation, Comments.NDisplacementAbruptVariation),
                (Names.EDisplacementAttentionLevel, Comments.EDisplacementAttentionLevel),
                (Names.EDisplacementAlertLevel, Comments.EDisplacementAlertLevel),
                (Names.EDisplacementEmergencyLevel, Comments.EDisplacementEmergencyLevel),
                (Names.EDisplacementAbruptVariation, Comments.EDisplacementAbruptVariation),
                (Names.PlanimetricDisplacementAttentionLevel, Comments.PlanimetricDisplacementAttentionLevel),
                (Names.PlanimetricDisplacementAlertLevel, Comments.PlanimetricDisplacementAlertLevel),
                (Names.PlanimetricDisplacementEmergencyLevel, Comments.PlanimetricDisplacementEmergencyLevel),
                (Names.PlanimetricDisplacementAbruptVariation, Comments.PlanimetricDisplacementAbruptVariation),
                (Names.ZDisplacementAttentionLevel, Comments.ZDisplacementAttentionLevel),
                (Names.ZDisplacementAlertLevel, Comments.ZDisplacementAlertLevel),
                (Names.ZDisplacementEmergencyLevel, Comments.ZDisplacementEmergencyLevel),
                (Names.ZDisplacementAbruptVariation, Comments.ZDisplacementAbruptVariation)
            };

            var ws = workbook.SetColumnNamesAndComments(columns, "Prisma");

            for (int i = 2; i <= rows + 1; i++)
            {
                ws.Column(1).Cell(i).Value = searchIdentifier;
                ws.Column(8).Cell(i).Value = datum.ToString();
            }

            return workbook.SaveAsBytes();
        }
    }
}

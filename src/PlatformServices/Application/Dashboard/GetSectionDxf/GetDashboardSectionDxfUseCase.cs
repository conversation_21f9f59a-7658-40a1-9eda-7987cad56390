using System;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Core;
using Application.Core.Caching;
using Application.Extensions;
using Application.Section.Extensions;
using Application.Services.BlobStorage;
using Database.Repositories.Section;
using Database.Repositories.StaticMaterial;
using Database.Repositories.User;
using Domain.Entities;
using Domain.Enums;
using Dxf.Core;
using Dxf.Core.Extensions;
using IxMilia.Dxf;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Options;
using Model.Dashboard.GetSectionDxf.Request;
using Model.Dashboard.GetSectionDxf.Response;
using static Application.Core.UseCaseResponseFactory<Model.Dashboard.GetSectionDxf.Response.GetDashboardSectionDxfResponse>;

namespace Application.Dashboard.GetSectionDxf
{
    public sealed class GetDashboardSectionDxfUseCase : IGetDashboardSectionDxfUseCase
    {
        private readonly ISectionRepository _sectionRepository;
        private readonly IUserRepository _userRepository;
        private readonly IStaticMaterialRepository _staticMaterialRepository;
        private readonly IDistributedCache _cache;
        private readonly IBlobStorageService _blobService;
        private readonly BlobStorageOptions _blobOptions;
        private readonly GetDashboardSectionDxfRequestValidator _requestValidator = new();

        public GetDashboardSectionDxfUseCase(ISectionRepository sectionRepository,
            IUserRepository userRepository,
            IBlobStorageService blobService,
            IOptions<BlobStorageOptions> blobOptions,
            IStaticMaterialRepository staticMaterialRepository,
            IDistributedCache cache)
        {
            _sectionRepository = sectionRepository;
            _userRepository = userRepository;
            _blobService = blobService;
            _blobOptions = blobOptions.Value;
            _staticMaterialRepository = staticMaterialRepository;
            _cache = cache;
        }

        public async Task<UseCaseResponse<GetDashboardSectionDxfResponse>> Execute(GetDashboardSectionDxfRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest(null);
                }

                await request.AddRequesterStructures(_userRepository);

                var validationResult = await _requestValidator
                    .ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    return BadRequest(null,
                        validationResult.Errors.ToErrorMessages());
                }
                
                var cacheKey =
                    CacheKeyGenerator.Generate(request.WithDefaultMetadata());
            
                var result = await _cache.GetAsync<GetDashboardSectionDxfResponse>(
                    cacheKey,
                    CancellationToken.None);

                if (result is not null)
                {
                    return Ok(result);
                }

                var section = await _sectionRepository
                    .GetAsync(request.SectionId);

                if (section == null)
                {
                    return NoContent();
                }

                if (!request.RequestedBySuperSupport
                    && !request.RequestedUserStructures.Any(x => x == section.Structure.Id))
                {
                    return Forbidden(null, "000", "You not allowed to request information from this section.");
                }

                if (!section.Reviews.Any())
                {
                    return BadRequest(null, "000", "Section has no reviews.");
                }

                byte[] file = null;

                var usedReview = new SectionReview();

                foreach (var review in section
                    .Reviews
                    .OrderByDescending(x => x.StartDate))
                {
                    usedReview = review;

                    if (review.ConstructionStages.Any())
                    {
                        var currentStage = review.ConstructionStages.FirstOrDefault(x => x.IsCurrentStage);

                        if (currentStage != null && currentStage.Drawing != null)
                        {
                            file = await _blobService
                                .GetAsync(currentStage.Drawing.UniqueName, _blobOptions.ClientsContainer);

                            break;
                        }
                        else
                        {
                            foreach (var stage in review.ConstructionStages)
                            {
                                if (stage.Drawing != null)
                                {
                                    file = await _blobService
                                        .GetAsync(stage.Drawing.UniqueName, _blobOptions.ClientsContainer);

                                    break;
                                }
                            }

                            break;
                        }
                    }
                    else if (review.Drawing != null)
                    {
                        file = await _blobService
                            .GetAsync(review.Drawing.UniqueName, _blobOptions.ClientsContainer);

                        break;
                    }
                }

                if (file == null)
                {
                    return BadRequest(null, "000", "There is no drawing for this section.");
                }

                using var inputStream = new MemoryStream(file);
                var dxfFile = DxfFile.Load(inputStream);
                
                dxfFile.Normalize();

                if (!dxfFile.HasEntities())
                {
                    return BadRequest(null, "000", "The DXF file is empty.");
                }

                if (!dxfFile.HasLayers())
                {
                    return BadRequest(null, "000", "The DXF file has no layers.");
                }
                
                var materialsSearchIds = dxfFile.GetMaterialsSearchIdentifiers();

                var materials = await _staticMaterialRepository
                    .GetNameBySearchIdAsync(materialsSearchIds);
                
                dxfFile.SetLayerColors();
                dxfFile.SetMaterialLayersNames(materials);

                var validInstruments = section
                    .Instruments
                    .Where(x => x.Type is InstrumentType.WaterLevelIndicator
                                    or InstrumentType.ElectricPiezometer
                                    or InstrumentType.OpenStandpipePiezometer
                                && x.Online)
                    .ToList();

                dxfFile.AddInstruments(validInstruments, section);

                using var outputStream = new MemoryStream();

                dxfFile.SaveAs(outputStream);
                var resultFile = outputStream.ToArray();

                result = new GetDashboardSectionDxfResponse
                {
                    Base64 = Convert.ToBase64String(resultFile),
                    Name = $"{section.Name}.dxf",
                    SectionReviewIndex = usedReview.Index,
                    SectionReviewStartDate = usedReview.StartDate
                };
                
                await _cache.SetAsync(
                    cacheKey,
                    result,
                    new DistributedCacheEntryOptions()
                    {
                        AbsoluteExpirationRelativeToNow =
                            TimeSpan.FromMinutes(1)
                    });

                return Ok(result);
            }
            catch (Exception e)
            {
                return InternalServerError(null, errors: e.ToErrorMessages("000"));
            }
        }
    }
}

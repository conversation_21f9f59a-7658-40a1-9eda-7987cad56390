using Application.Core;
using Application.Core.Caching;
using Application.Extensions;
using Database.Repositories.Package;
using Database.Repositories.User;
using Microsoft.Extensions.Caching.Distributed;
using Model.Core.Search.Pagination;
using Model.Package.Search.Request;
using System;
using System.Threading.Tasks;
using Model.Core.Search.DateFilter;
using static Application.Core.UseCaseResponseFactory<Model.Core.Search.Pagination.PaginationResponse>;

namespace Application.Package.Search
{
    public sealed class SearchPackageUseCase : ISearchPackageUseCase
    {
        private readonly IPackageRepository _packageRepository;
        private readonly IUserRepository _userRepository;
        private readonly IDistributedCache _cache;
        private readonly SearchPackageRequestValidator _requestValidator = new();

        public SearchPackageUseCase(
            IPackageRepository packageRepository,
            IUserRepository userRepository,
            IDistributedCache cache)
        {
            _packageRepository = packageRepository;
            _userRepository = userRepository;
            _cache = cache;
        }

        public async Task<UseCaseResponse<PaginationResponse>> Execute(SearchPackageRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest(null, "000", "Request cannot be empty.");
                }

                await request.AddRequesterStructures(_userRepository);

                var validationResult = await _requestValidator
                    .ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    return BadRequest(null, validationResult.Errors.ToErrorMessages());
                }
                
                request.SetDateFilters();

                var packages = await _cache.GetOrCreateAsync(
                    key: CacheKeyGenerator.Generate(request),
                    factory: () => _packageRepository.SearchAsync(request),
                    options: new DistributedCacheEntryOptions()
                    {
                        AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(5)
                    });

                if (packages == null || packages.CurrentItemsCount == 0)
                {
                    return NoContent();
                }

                return Ok(packages);
            }
            catch (Exception e)
            {
                return InternalServerError(null, errors: e.ToErrorMessages("000"));
            }
        }
    }
}

using Application.Core;
using Application.Extensions;
using Database.Repositories.ActionPlan;
using Database.Repositories.User;
using Model.ActionPlan.Search.Request;
using Model.Core.Search.DateFilter;
using Model.Core.Search.Pagination;
using System;
using System.Linq;
using System.Threading.Tasks;
using static Application.Core.UseCaseResponseFactory<Model.Core.Search.Pagination.PaginationResponse>;

namespace Application.ActionPlan.Search
{
    public sealed class SearchActionPlanUseCase : ISearchActionPlanUseCase
    {
        private readonly IActionPlanRepository _actionPlanRepository;
        private readonly IUserRepository _userRepository;
        private readonly SearchActionPlanRequestValidator _requestValidator = new();

        public SearchActionPlanUseCase(
            IUserRepository userRepository, IActionPlanRepository actionPlanRepository)
        {
            _userRepository = userRepository;
            _actionPlanRepository = actionPlanRepository;
        }

        public async Task<UseCaseResponse<PaginationResponse>> Execute(SearchActionPlanRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest(null, "000", "Request cannot be empty.");
                }

                await request.AddRequesterStructures(_userRepository);

                var validationResult = await _requestValidator
                    .ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    return BadRequest(null, validationResult.Errors.ToErrorMessages());
                }

                request.SetDateFilters();

                var inspectionSheets = await _actionPlanRepository.SearchAsync(request);

                if (inspectionSheets == null || !inspectionSheets.Any())
                {
                    return NoContent();
                }

                var totalInspectionSheets = await _actionPlanRepository.CountAsync(request);

                return Ok(new PaginationResponse
                {
                    Data = inspectionSheets,
                    TotalItemsCount = totalInspectionSheets,
                    CurrentItemsCount = inspectionSheets.Count()
                });
            }
            catch (Exception e)
            {
                return InternalServerError(null, errors: e.ToErrorMessages("000"));
            }
        }
    }
}

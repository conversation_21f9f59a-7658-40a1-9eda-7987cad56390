using Application.Extensions;
using ClosedXML.Excel;
using static Application.Reading.GetTemplateFile.TemplateHeaders;

namespace Application.Reading.GetTemplateFile.Strategy.Behavior
{
    public class XlsxSettlementGaugeReadingStrategy : IFileStrategy
    {
        public byte[] GetFile()
        {
            var workbook = new XLWorkbook();

            var columns = new (string, string)[]
            {
                (Names.InstrumentIdentifier, Comments.InstrumentIdentifier),
                (Names.MagneticRingIdentifier, Comments.MagneticRingIdentifier),
                (Names.DateAndTime, Comments.DateAndTime),
                (Names.AbsoluteDepth, Comments.AbsoluteDepth),
                (Names.AbsoluteSettlement, Comments.AbsoluteSettlement),
            };

            workbook.SetColumnNamesAndComments(columns, "Leituras de med. de recalque");
            return workbook.SaveAsBytes();
        }
    }
}

using Application.Extensions;
using Coordinate.Core.Enums;
using Model.Reading._Shared.ConvertReadings;
using Model.Reading._Shared.ReadingValue;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Domain.Entities;
using Domain.Extensions;

namespace Application.Reading.ConvertFileToAdd.Strategy.Behavior
{
    public class SurfaceLandmarkAndPrismReadingConverter : IReadingConverter
    {
        public Task ConvertRowAsync(
            DataRow row, 
            List<ConvertedReading> readings, 
            Domain.Entities.Instrument instrument,
            Domain.Entities.Reading referentialReading)
        {
            var isReferential = referentialReading == null 
                && !readings.Exists(reading => reading.IsReferential.Value && reading.Instrument.Id == instrument.Id);

            var reading = new ConvertedReading()
            {
                Instrument = instrument.Convert(),
                IsReferential = isReferential
            };

            readings.Add(reading);

            _ = decimal.TryParse(row[3].ToString(), out decimal eastCoordinate);
            _ = decimal.TryParse(row[4].ToString(), out decimal northCoordinate);
            _ = decimal.TryParse(row[5].ToString(), out decimal quota);
            
            eastCoordinate = Math.Round(eastCoordinate, ReadingValue.UtmCoordinatesDecimalPlaces);
            northCoordinate = Math.Round(northCoordinate, ReadingValue.UtmCoordinatesDecimalPlaces);
            quota = Math.Round(quota, ReadingValue.QuotaDecimalPlaces);

            var referentialEastCoordinate = referentialReading?.Values?[0]?.EastCoordinate;
            var referentialNorthCoordinate = referentialReading?.Values?[0]?.NorthCoordinate; 
            
            var eastDisplacement = ReadingValue.CalculateUtmCoordinateDisplacement(isReferential, eastCoordinate, referentialEastCoordinate);
            var northDisplacement = ReadingValue.CalculateUtmCoordinateDisplacement(isReferential, northCoordinate, referentialNorthCoordinate);
            var totalPlanimetricDisplacement = ReadingValue.CalculateTotalPlanimetricDisplacement(northDisplacement, eastDisplacement);

            var referentialQuota = referentialReading?.Values?[0]?.Quota;

            var zDisplacement = ReadingValue.CalculateZDisplacement(isReferential, quota, referentialQuota);
            var aDisplacement = ReadingValue.CalculateADisplacement(northDisplacement, eastDisplacement, instrument.Azimuth);
            var bDisplacement = ReadingValue.CalculateBDisplacement(northDisplacement, eastDisplacement, instrument.Azimuth);

            var readingValueRequest = new ReadingValueRequest
            {
                Date = row[1].ToString().ToDateTime(),
                Datum = Enum.TryParse(row[2].ToString(), out Datum datum) ? datum : Datum.SIRGAS2000,
                Quota = quota,
                EastCoordinate = eastCoordinate,
                NorthCoordinate =  northCoordinate,
                EastDisplacement = eastDisplacement,
                NorthDisplacement = northDisplacement,
                ZDisplacement = zDisplacement,
                TotalPlanimetricDisplacement = totalPlanimetricDisplacement,
                ADisplacement = aDisplacement,
                BDisplacement = bDisplacement
            };

            reading.Values.Add(readingValueRequest);

            return Task.CompletedTask;
        }
    }
}

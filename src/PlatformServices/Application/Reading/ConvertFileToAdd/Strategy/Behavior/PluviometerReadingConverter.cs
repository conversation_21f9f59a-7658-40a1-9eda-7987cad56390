using Application.Extensions;
using Model.Reading._Shared.ConvertReadings;
using Model.Reading._Shared.ReadingValue;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Threading.Tasks;
using Domain.Entities;
using Domain.Extensions;

namespace Application.Reading.ConvertFileToAdd.Strategy.Behavior
{
    public class PluviometerReadingConverter : IReadingConverter
    {
        public Task ConvertRowAsync(
            DataRow row, 
            List<ConvertedReading> readings, 
            Domain.Entities.Instrument instrument,
            Domain.Entities.Reading referentialReading)
        {
            var reading = new ConvertedReading()
            {
                Instrument = instrument.Convert()
            };

            readings.Add(reading);

            var readingValueRequest = new ReadingValueRequest
            {
                Date = row[1].ToString().ToDateTime(),
                Pluviometry = decimal.TryParse(row[2].ToString(), out decimal pluviometry) ? Math.Round(pluviometry, ReadingValue.QuotaDecimalPlaces) : 0
            };

            reading.Values.Add(readingValueRequest);

            return Task.CompletedTask;
        }
    }
}

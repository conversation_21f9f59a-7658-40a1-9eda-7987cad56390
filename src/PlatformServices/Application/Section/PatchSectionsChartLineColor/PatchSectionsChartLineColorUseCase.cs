using Application.Core;
using Application.Extensions;
using Database.Repositories.Section;
using Database.Repositories.User;
using Model.Section.PatchSectionsChartLineColor.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Application.Core.UseCaseResponseFactory<System.Collections.Generic.IEnumerable<System.Guid>>;

namespace Application.Section.PatchSectionsChartLineColor
{
    public class PatchSectionsChartLineColorUseCase : IPatchSectionsChartLineColorUseCase
    {
        private readonly IUserRepository _userRepository;
        private readonly ISectionRepository _sectionRepository;
        private readonly PatchSectionsChartLineColorRequestValidator _requestValidator;

        public PatchSectionsChartLineColorUseCase(
            IUserRepository userRepository, 
            ISectionRepository sectionRepository)
        {
            _userRepository = userRepository;
            _sectionRepository = sectionRepository;
            _requestValidator = new();
        }

        public async Task<UseCaseResponse<IEnumerable<Guid>>> Execute(
            PatchSectionsChartLineColorRequest request)
        {

            if (request?.ColorsBySections == null)
            {
                return BadRequest(
                    null);
            }

            try
            {
                var validationResult = await _requestValidator
                    .ValidateAsync(request);
                if (!validationResult.IsValid)
                {
                    return BadRequest(
                        null,
                        validationResult.Errors.ToErrorMessages());
                }

                var sectionsId = request.ColorsBySections.Select(x => x.Id);

                var structureIdsBySections = await _sectionRepository
                    .GetStructuresIdBySectionsAsync(sectionsId);
                if (structureIdsBySections == null || !structureIdsBySections.Any())
                {
                    return BadRequest(
                        null,
                        "000",
                        "Sections not found in database.");
                }

                var sectionsIdNotFound = sectionsId.Except(structureIdsBySections.Select(s => s.Id));
                if (sectionsIdNotFound.Any())
                {
                    return BadRequest(
                        null,
                        "000",
                        $"Sections identified by ids: {string.Join(';', sectionsIdNotFound)} were not found.");
                }

                var structuresId = structureIdsBySections.Select(s => s.StructureId).Distinct();
                if (structuresId.Count() > 1)
                {
                    return BadRequest(
                        null,
                        "000",
                        "All sections must belong to the same structure.");
                }

                await request.AddRequesterStructures(_userRepository);

                if (!request.RequestedBySuperSupport
                    && !request.RequestedUserStructures.Exists(x => x == structuresId.First()))
                {
                    return Forbidden(
                        null, 
                        "000",
                        "User does not have access to this structure.");
                }

                await _sectionRepository.UpdateChartLineColorByIdsAsync(request.ColorsBySections);

                return Ok(
                    sectionsId);
            }
            catch (Exception e)
            {
                return InternalServerError(
                    null, 
                    errors: e.ToErrorMessages("000"));
            }
        }
    }
}

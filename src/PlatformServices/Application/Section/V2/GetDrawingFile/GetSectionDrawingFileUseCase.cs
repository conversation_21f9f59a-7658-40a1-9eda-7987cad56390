using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Application.Core;
using Application.Core.Caching;
using Application.Extensions;
using Application.Section.Extensions;
using Application.Services.BlobStorage;
using Database.Repositories.Section;
using Database.Repositories.StaticMaterial;
using Database.Repositories.User;
using Dxf.Core;
using Dxf.Core.Extensions;
using IxMilia.Dxf;
using Microsoft.Extensions.Caching.Distributed;
using Model.Section.V2.GetDrawingFile.Request;
using static Application.Core.UseCaseResponseFactory<Model._Shared.File.File>;
using File = Model._Shared.File.File;

namespace Application.Section.V2.GetDrawingFile;

public class GetSectionDrawingFileUseCase : IGetSectionDrawingFileUseCase
{
    private readonly ISectionRepository _sectionRepository;
    private readonly IUserRepository _userRepository;
    private readonly IStaticMaterialRepository _staticMaterialRepository;
    private readonly IBlobStorageService _blobStorageService;
    private readonly IDistributedCache _cache;

    private readonly GetSectionDrawingFileRequestValidator _requestValidator =
        new();

    public GetSectionDrawingFileUseCase(
        ISectionRepository sectionRepository,
        IUserRepository userRepository,
        IBlobStorageService blobStorageService,
        IStaticMaterialRepository staticMaterialRepository,
        IDistributedCache cache)
    {
        _sectionRepository = sectionRepository;
        _userRepository = userRepository;
        _blobStorageService = blobStorageService;
        _staticMaterialRepository = staticMaterialRepository;
        _cache = cache;
    }

    public async Task<UseCaseResponse<File>> Execute(
        GetSectionDrawingFileRequest request)
    {
        if (request == null)
        {
            return BadRequest(null, "000", "Request cannot be null.");
        }

        try
        {
            await request.AddRequesterStructures(_userRepository);

            var validationResult = await _requestValidator
                .ValidateAsync(request);

            if (!validationResult.IsValid)
            {
                return BadRequest(
                    null,
                    validationResult.Errors.ToErrorMessages());
            }

            var cacheKey =
                CacheKeyGenerator.Generate(request.WithDefaultMetadata());
            
            var result = await _cache.GetAsync<File>(
                cacheKey,
                CancellationToken.None);

            if (result is not null)
            {
                return Ok(result);
            }

            var file = await _sectionRepository.GetSectionDrawingAsync(request);

            if (file is null)
            {
                return NoContent();
            }

            var bytes = await _blobStorageService.GetAsync(file.UniqueName);

            using var inputStream = new MemoryStream(bytes);
            var dxfFile = DxfFile.Load(inputStream);

            dxfFile.Normalize();

            if (!dxfFile.HasEntities())
            {
                return BadRequest(null, "000", "The DXF file is empty.");
            }

            if (!dxfFile.HasLayers())
            {
                return BadRequest(null, "000", "The DXF file has no layers.");
            }

            var materialsSearchIds = dxfFile.GetMaterialsSearchIdentifiers();

            var materials = await _staticMaterialRepository
                .GetNameBySearchIdAsync(materialsSearchIds);

            dxfFile.SetLayerColors();
            dxfFile.SetMaterialLayersNames(materials);

            using var outputStream = new MemoryStream();
            dxfFile.SaveAs(outputStream);

            result = new File
            {
                Name = file.Name,
                Base64 = Convert.ToBase64String(outputStream.ToArray())
            };

            await _cache.SetAsync(
                cacheKey,
                result,
                new DistributedCacheEntryOptions()
                {
                    AbsoluteExpirationRelativeToNow =
                        TimeSpan.FromMinutes(1)
                });

            return Ok(result);
        }
        catch (Exception e)
        {
            return InternalServerError(null, errors: e.ToErrorMessages("000"));
        }
    }
}

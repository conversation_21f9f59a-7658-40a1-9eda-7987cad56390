using System.Collections.Generic;
using System.Linq;
using IxMilia.Dxf;
using IxMilia.Dxf.Entities;
using Dxf.Core;
using DxfPoint = IxMilia.Dxf.DxfPoint;

namespace Application.Section.Extensions;

public static class DxfFileExtensions
{
    public static (bool IsValid, string ErrorMessage)
        IsValidSection(
            this DxfFile dxfFile,
            string dxfName,
            List<Domain.Entities.StaticMaterial> materials)
    {
        if (!dxfFile.HasEntities())
        {
            return (false, $"The DXF file {dxfName} is empty.");
        }

        if (!dxfFile.HasLayers())
        {
            return (false, $"The DXF file {dxfName} has no layers.");
        }

        if (!dxfFile.HasFillingEntities() &&
            !dxfFile.HasInsertEntitiesWithSolid())
        {
            return
                (false,
                    $"The DXF file {dxfName} has no filling entities like hatch or solid.");
        }

        if (dxfFile.HasSplineEntities())
        {
            return (false, $"The DXF file {dxfName} uses Splines, which is not supported.");
        }

        if (!dxfFile.HasExternalLayer())
        {
            return (false,
                $"The DXF file {dxfName} has no layer named 'external'.");
        }

        var materialsSearchIds = dxfFile.GetMaterialsSearchIdentifiers();

        if (!materialsSearchIds.Any())
        {
            return
                (false,
                    $"The DXF file {dxfName} has no layers with hatch entities and names corresponding to the static material id.");
        }

        if (materials == null || !materials.Any())
        {
            return (false,
                $"No materials found in database for DXF {dxfName}.");
        }

        var dbMaterialsSearchIds = materials
            .Select(x => x.SearchIdentifier)
            .ToList();

        var allMaterialsSearchIdExists = materialsSearchIds
            .All(searchId => dbMaterialsSearchIds.Contains(searchId));

        if (!allMaterialsSearchIdExists)
        {
            return
                (false,
                    $"The DXF file {dxfName} has hatch layers that do not correspond to any material in the structure.");
        }

        var inactiveMaterials = materials
            .Where(x => !x.Active)
            .ToList();

        if (inactiveMaterials.Any())
        {
            return
                (false,
                    $"The DXF file {dxfName} has inactive materials: {string.Join(", ", inactiveMaterials.Select(x => x.Name))}");
        }

        if (!dxfFile.HasLimitsDefined())
        {
            return (true, string.Empty);
        }

        var circles = dxfFile.GetSlopeLimitsCircles();

        if (!circles.Any())
        {
            return
                (false,
                    $"The DXF file {dxfName} has limits defined but no circle entities.");
        }

        if (dxfFile.SlopeLimitsAreOutsideExternalBoundary())
        {
            return
                (false,
                    $"The DXF file {dxfName} has circles to define the slope limit outside the limits defined by the external layer.");
        }

        if (circles.Count != 2 && circles.Count != 4)
        {
            return
                (false,
                    $"The DXF file {dxfName} has {circles.Count} circles to define the slope limit. It must have 2 or 4 circles.");
        }

        if (circles.Count == 2)
        {
            var orderedCircles = circles.OrderBy(x => x.Center.X).ToList();

            if (orderedCircles[0].Center.X > orderedCircles[1].Center.X)
            {
                return
                    (false,
                        $"The DXF file {dxfName} has two circles to define the slope limit, but the first circle is after the second.");
            }
            else if (orderedCircles[0]
                     .Center
                     .X
                     .Equals(orderedCircles[1].Center.X))
            {
                return
                    (false,
                        $"The DXF file {dxfName} has two circles to define the slope limit, but the first circle is in the same position as the second.");
            }
        }
        else if (circles.Count == 4)
        {
            var orderedCircles = circles.OrderBy(x => x.Center.X).ToList();

            if (orderedCircles[0].Center.X > orderedCircles[1].Center.X
                || orderedCircles[1].Center.X > orderedCircles[2].Center.X
                || orderedCircles[2].Center.X > orderedCircles[3].Center.X)
            {
                return
                    (false,
                        $"The DXF file {dxfName} has four circles to define the slope limit, but the first two circles are after the second two.");
            }
            else if (orderedCircles[0]
                         .Center
                         .X
                         .Equals(orderedCircles[1].Center.X)
                     || orderedCircles[2]
                         .Center
                         .X
                         .Equals(orderedCircles[3].Center.X))
            {
                return
                    (false,
                        $"The DXF file {dxfName} has four circles to define the slope limit, but some circles are in the same position.");
            }
        }

        return (true, string.Empty);
    }

    public static void SetMaterialLayersNames(
        this DxfFile dxfFile,
        List<Domain.Entities.StaticMaterial> materials)
    {
        foreach (var layer in dxfFile.Layers)
        {
            if (!int.TryParse(layer.Name, out var searchId))
            {
                continue;
            }
            
            var material =
                materials.FirstOrDefault(x => x.SearchIdentifier == searchId);

            if (material == null)
            {
                continue;
            }

            layer.Name = $" {material.Name}";

            dxfFile.Entities
                .Where(x => x.Layer == searchId.ToString())
                .ToList()
                .ForEach(x => x.Layer = layer.Name);

            dxfFile.Entities
                .Where(x => x.EntityType == DxfEntityType.Insert)
                .Select(x => (DxfInsert)x)
                .Where(x => x.Entities.Any(y => y.Layer == searchId.ToString()))
                .ToList()
                .ForEach(x => x.Entities
                    .Where(y => y.Layer == searchId.ToString())
                    .ToList()
                    .ForEach(y => y.Layer = layer.Name));

            dxfFile.Blocks
                .Where(x => x.Layer == searchId.ToString())
                .ToList()
                .ForEach(x => x.Layer = layer.Name);

            dxfFile.Blocks
                .SelectMany(x => x.Entities)
                .Where(x => x.Layer == searchId.ToString())
                .ToList()
                .ForEach(x => x.Layer = layer.Name);

            dxfFile.Header.CurrentLayer = layer.Name;
        }
    }

    public static void AddInstruments(
        this DxfFile dxfFile,
        List<Domain.Entities.Instrument> validInstruments,
        Domain.Entities.Section section)
    {
        if (!validInstruments.Any())
        {
            return;
        }
        
        var points = validInstruments
            .Select(instrument => dxfFile.AddInstrument(instrument, section))
            .ToList();

        dxfFile.AddInstrumentIdentifier(points);
    }
}

<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Instrument\Convert\**" />
    <EmbeddedResource Remove="Instrument\Convert\**" />
    <None Remove="Instrument\Convert\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Storage.Blobs" Version="12.14.1" />
    <PackageReference Include="ClosedXML" Version="0.100.3" />
    <PackageReference Include="CompareNETObjects" Version="4.78.0" />
    <PackageReference Include="ExcelDataReader.DataSet" Version="3.6.0" />
    <PackageReference Include="IxMilia.Dxf" Version="0.8.3" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.2.5" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.Abstractions" Version="8.0.0" />
    <PackageReference Include="Refit" Version="7.2.22" />
    <PackageReference Include="Refit.HttpClientFactory" Version="7.2.22" />
    <PackageReference Include="Serilog" Version="2.10.0" />
    <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="8.0.0" />
    <PackageReference Include="System.Linq.Async" Version="6.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Application.Core\Application.Core.csproj" />
    <ProjectReference Include="..\Database\Database.csproj" />
    <ProjectReference Include="..\Dxf.Core\Dxf.Core.csproj" />
  </ItemGroup>
</Project>
using Application.Core;
using Application.Extensions;
using Database.Repositories.InspectionSheet;
using Database.Repositories.User;
using Model.Core.Search.Pagination;
using Model.InspectionSheet.SearchHistory.Request;
using Model.InspectionSheet.SearchHistory.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Application.Core.UseCaseResponseFactory<Model.Core.Search.Pagination.PaginationResponse>;

namespace Application.InspectionSheet.SearchHistory
{
    public sealed class SearchInspectionSheetHistoryUseCase : ISearchInspectionSheetHistoryUseCase
    {
        private readonly IInspectionSheetRepository _inspectionSheetRepository;
        private readonly IUserRepository _userRepository;
        private readonly SearchInspectionSheetHistoryRequestValidator _requestValidator = new();

        public SearchInspectionSheetHistoryUseCase(
            IInspectionSheetRepository inspectionSheetRepository,
            IUserRepository userRepository)
        {
            _inspectionSheetRepository = inspectionSheetRepository;
            _userRepository = userRepository;
        }

        public async Task<UseCaseResponse<PaginationResponse>> Execute(SearchInspectionSheetHistoryRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest(null, "000", "Request cannot be null.");
                }

                await request.AddRequesterStructures(_userRepository);

                var validationResult = await _requestValidator
                    .ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    return BadRequest(null,
                        validationResult.Errors.ToErrorMessages());
                }

                var sectionHistory = await _inspectionSheetRepository
                    .SearchHistoryAsync(request);

                if (sectionHistory == null || !sectionHistory.Any())
                {
                    return NoContent();
                }

                var totalSectionHistory = await _inspectionSheetRepository
                    .CountHistoryAsync(request);

                return Ok(Map(sectionHistory, totalSectionHistory));
            }
            catch (Exception e)
            {
                return InternalServerError(null, errors: e.ToErrorMessages("000"));
            }
        }

        private static PaginationResponse Map(IEnumerable<SearchInspectionSheetHistoryResponse> inspectionSheetHistory, int totalInspectionSheetHistory)
        {
            return new PaginationResponse
            {
                Data = inspectionSheetHistory,
                CurrentItemsCount = inspectionSheetHistory.Count(),
                TotalItemsCount = totalInspectionSheetHistory,
            };
        }
    }
}

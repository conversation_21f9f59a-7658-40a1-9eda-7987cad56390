namespace Geometry.Core
{
    public static class Helper
    {
        public static bool IsPointInPolygon(PointD[] polygon, PointD testPoint)
        {
            var result = false;
            var j = polygon.Length - 1;

            for (int i = 0; i < polygon.Length; i++)
            {
                if (polygon[i].Y < testPoint.Y && polygon[j].Y >= testPoint.Y ||
                    polygon[j].Y < testPoint.Y && polygon[i].Y >= testPoint.Y)
                {
                    if (polygon[i].X + (testPoint.Y - polygon[i].Y) /
                       (polygon[j].Y - polygon[i].Y) *
                       (polygon[j].X - polygon[i].X) < testPoint.X)
                    {
                        result = !result;
                    }
                }
                j = i;
            }

            return result;
        }

        public static PointD GetCentroid(PointD[] triangle)
        {
            var centroidX = (triangle[0].X + triangle[1].X + triangle[2].X) / 3;
            var centroidY = (triangle[0].Y + triangle[1].Y + triangle[2].Y) / 3;

            return new PointD(centroidX, centroidY);
        }

        public static double GetInstrumentXPoint(
            PointD referenceOriginPoint,
            PointD sectionUpstreamPoint,
            PointD sectionDownstreamPoint,
            PointD sectionMidpoint,
            PointD instrumentPoint)
        {
            var upstreamToMidpointInstrumentProjection =
                CalculateIntersectionPoint(
                    instrumentPoint,
                    sectionUpstreamPoint,
                    sectionMidpoint);
            
            var distanceFromUpstreamToUpstreamProjection = GetDistance(
                upstreamToMidpointInstrumentProjection,
                sectionUpstreamPoint);

            var midpointToDownstreamInstrumentProjection =
                CalculateIntersectionPoint(
                    instrumentPoint,
                    sectionMidpoint,
                    sectionDownstreamPoint);
            
            var distanceFromDownstreamProjectionToDownstream = GetDistance(
                midpointToDownstreamInstrumentProjection,
                sectionDownstreamPoint);

            var distanceFromUpstreamToMidpoint = GetDistance(
                sectionUpstreamPoint,
                sectionMidpoint);
            
            var distanceFromDownstreamToMidpoint = GetDistance(
                sectionDownstreamPoint,
                sectionMidpoint);

            /*
             * True: The instrument's projection onto the upstream-midpoint segment falls between the upstream point and midpoint
             * False: The instrument's projection extends beyond the midpoint (closer to or past the midpoint)
             */
            var upstreamProjectionIsCloserToUpstreamThanMidpoint =
                distanceFromUpstreamToUpstreamProjection <
                distanceFromUpstreamToMidpoint;

            /*
             * True: The instrument's projection onto the midpoint-downstream segment extends beyond the downstream point
             * False: The instrument's projection falls between the midpoint and downstream point
             */
            var downstreamProjectionIsFartherFromDownstreamThanMidpoint =
                distanceFromDownstreamProjectionToDownstream >
                distanceFromDownstreamToMidpoint;

            if (!upstreamProjectionIsCloserToUpstreamThanMidpoint &&
                downstreamProjectionIsFartherFromDownstreamThanMidpoint)
            {
                // Instrument point is the midpoint
                return distanceFromUpstreamToMidpoint + referenceOriginPoint.X;
            }

            if (!downstreamProjectionIsFartherFromDownstreamThanMidpoint)
            {
                // Instrument point is located between midpoint and downstream
                var distanceFromMidpointToInstrument = GetDistance(sectionMidpoint, instrumentPoint);
                
                return distanceFromMidpointToInstrument +
                       distanceFromUpstreamToMidpoint + referenceOriginPoint.X;
            }

            // Instrument point is located between upstream and midpoint
            return distanceFromUpstreamToUpstreamProjection +
                   referenceOriginPoint.X;
        }

        public static double GetInstrumentXPoint(
            PointD upstreamCoordinateInFile,
            PointD sectionUpstream,
            PointD sectionDownstream,
            PointD instrumentCoordinate)
        {
            var perpendicularPoint = CalculateIntersectionPoint(instrumentCoordinate, sectionUpstream, sectionDownstream);

            var distance = GetDistance(perpendicularPoint, sectionUpstream);

            return distance + upstreamCoordinateInFile.X;
        }

        public static PointD CalculateIntersectionPoint(PointD refPoint, PointD point1, PointD point2)
        {
            var a = (point2.Y - point1.Y) / (point2.X - point1.X);
            var b = ((point1.Y * point2.X) - (point1.X * point2.Y)) / (point2.X - point1.X);

            var m = -1 / a;
            var n = (refPoint.Y + (((point2.X - point1.X) / (point2.Y - point1.Y)) * refPoint.X));

            var xA = (n - b) / (a - m);
            var yA = a * xA + b;

            xA = double.IsNaN(xA) ? 0 : xA;
            yA = double.IsNaN(yA) ? 0 : yA;

            return new(xA, yA);
        }

        public static double GetDistance(PointD point1, PointD point2)
        {
            return Math.Sqrt(Math.Pow(point1.X - point2.X, 2) + Math.Pow(point1.Y - point2.Y, 2));
        }

        public static List<double> FindXDoesIntersectPolygon(double y, List<PointD> polygon)
        {
            var intersectingPoints = new List<double> ();

            var prevIndex = polygon.Count - 1;

            for (int i = 0; i < polygon.Count; i++)
            {
                var currentIndex = i;

                var startX = polygon[prevIndex].X;
                var startY = polygon[prevIndex].Y;
                var endX = polygon[currentIndex].X;
                var endY = polygon[currentIndex].Y;

                // Check if the line segment intersects with the horizontal line at coordinate y
                if ((startY <= y && endY >= y) || (startY >= y && endY <= y))
                {
                    // Calculate the x-coordinate of the intersection point using linear interpolation
                    var intersectX = startX + (y - startY) * (endX - startX) / (endY - startY);

                    // Add the intersecting point if it is not already present in the list
                    if (!intersectingPoints.Exists(p => p == intersectX))
                    {
                        intersectingPoints.Add(intersectX);
                    }
                }

                prevIndex = currentIndex;
            }

            return intersectingPoints;
        }

        public static bool CheckIfLineIntersectsPolygon(PointD start, PointD end, List<PointD> polygon)
        {
            for (int i = 0; i < polygon.Count; i++)
            {
                int j = (i + 1) % polygon.Count;

                if (IntersectsLine(start, end, polygon[i], polygon[j]) && !IsEndpoint(polygon[i], polygon[j], end))
                {
                    return true;
                }
            }

            return false;
        }

        public static double FindXDoesNotIntersectPolygon(PointD start, PointD valley, List<PointD> polygon)
        {
            var maxX = polygon.Max(p => p.X);
            var minX = polygon.Min(p => p.X);

            var decrement = 0.05f * maxX;

            var currentX = valley.X;

            while (currentX > minX &&
                CheckIfLineIntersectsPolygon(start, new PointD(currentX, valley.Y), polygon))
            {
                currentX -= decrement;
            }

            return currentX;
        }

        public static double FindYOnPolygonEdge(double x, List<PointD> polygon)
        {
            int numVertices = polygon.Count;

            for (int i = 0; i < numVertices - 1; i++)
            {
                var startX = polygon[i].X;
                var startY = polygon[i].Y;
                var endX = polygon[i + 1].X;
                var endY = polygon[i + 1].Y;

                if (x >= startX && x <= endX)
                {
                    var slope = (endY - startY) / (endX - startX);
                    var y = startY + slope * (x - startX);

                    return y;
                }
            }

            return double.MinValue;
        }

        private static bool IsEndpoint(PointD p1, PointD p2, PointD endpoint)
        {
            var minX = Math.Min(p1.X, p2.X);
            var maxX = Math.Max(p1.X, p2.X);
            var minY = Math.Min(p1.Y, p2.Y);
            var maxY = Math.Max(p1.Y, p2.Y);

            // Check if endpoint lies within the bounding box of the edge
            return (endpoint.X >= minX && endpoint.X <= maxX) && (endpoint.Y >= minY && endpoint.Y <= maxY);
        }

        private static bool IntersectsLine(PointD start, PointD end, PointD p1, PointD p2)
        {
            var d1 = CalculateDirection(start, end, p1);
            var d2 = CalculateDirection(start, end, p2);
            var d3 = CalculateDirection(p1, p2, start);
            var d4 = CalculateDirection(p1, p2, end);

            if (((d1 > 0 && d2 < 0) || (d1 < 0 && d2 > 0)) &&
                ((d3 > 0 && d4 < 0) || (d3 < 0 && d4 > 0)))
            {
                return true;
            }

            return false;
        }

        private static double CalculateDirection(PointD p1, PointD p2, PointD p3)
        {
            return ((p2.X - p1.X) * (p3.Y - p1.Y)) - ((p3.X - p1.X) * (p2.Y - p1.Y));
        }
    }
}
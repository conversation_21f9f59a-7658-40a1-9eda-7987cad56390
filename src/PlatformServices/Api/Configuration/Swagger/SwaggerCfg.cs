using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Mvc;

namespace Api.Configuration.Swagger
{
    public static class SwaggerCfg
    {
        public static IServiceCollection AddSwagger(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            services.AddSwaggerGen(options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo
                {
                    Title = "Api",
                    Version = "v1"
                });  
                
                options.SwaggerDoc("v2", new OpenApiInfo
                {
                    Title = "Api",
                    Version = "v2"
                });
                
                options.DocInclusionPredicate((version, apiDesc) =>
                {
                    var versions = apiDesc.ActionDescriptor.EndpointMetadata
                        .OfType<ApiVersionAttribute>()
                        .SelectMany(attr => attr.Versions);

                    return versions.Any(v => $"v{v.ToString()}" == version);
                });

                options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                {
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.OAuth2,
                    Flows = new OpenApiOAuthFlows
                    {
                        Implicit = new OpenApiOAuthFlow
                        {
                            Scopes = new Dictionary<string, string>
                            {
                                { "openid", "OpenId" },
                                { "profile", "Profile" }
                            },
                            TokenUrl = new Uri($"{configuration["Auth:BaseUrl"]}{configuration["Auth:TokenEndpoint"]}"),
                            AuthorizationUrl = new Uri($"{configuration["Auth:BaseUrl"]}{configuration["Auth:AuthorizationEndpoint"]}"),
                        }
                    }
                });

                options.EnableAnnotations();
                options.OperationFilter<SecurityRequirementsOperationFilter>();
                options.OperationFilter<JsonIgnoreQueryOperationFilter>();
                options.OperationFilter<FormdataIgnoreQueryOperationFilter>();
                options.OperationFilter<RemoveVersionFromParameter>();
                options.DocumentFilter<ReplaceVersionWithExactValueInPath>();
                options.OperationFilter<FileUploadOperationFilter>();
            });

            return services;
        }
    }
}
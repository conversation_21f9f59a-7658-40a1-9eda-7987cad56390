<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<UserSecretsId>794cd32d-49bf-4774-97d4-eb0d5c348259</UserSecretsId>
	</PropertyGroup>

	<ItemGroup>
		<None Include="..\..\.editorconfig" Link=".editorconfig" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.2.2" />
		<PackageReference Include="Azure.Identity" Version="1.16.0" />
		<PackageReference Include="Azure.Monitor.OpenTelemetry.AspNetCore" Version="1.2.0" />
		<PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.4.0" />
		<PackageReference Include="MassTransit" Version="8.0.3" />
		<PackageReference Include="MassTransit.Extensions.DependencyInjection" Version="7.3.1" />
		<PackageReference Include="MassTransit.RabbitMQ" Version="8.0.3" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.4" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.0.0" />
		<PackageReference Include="NewRelic.LogEnrichers.Serilog" Version="1.0.1" />
		<PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.9.0" />
		<PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.9.0" />
		<PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.9.0" />
		<PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.9.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="4.1.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="5.6.3" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="5.6.3" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Application\Application.csproj" />
		<ProjectReference Include="..\Model.Core\Model.Core.csproj" />
	</ItemGroup>

	<ProjectExtensions>
		<VisualStudio>
			<UserProperties appsettings_1json__JsonSchema="" />
		</VisualStudio>
	</ProjectExtensions>

</Project>